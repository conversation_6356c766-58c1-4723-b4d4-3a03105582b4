"""
نظام الإشعارات
"""

import json
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Callable
from kivy.clock import Clock
from kivymd.uix.snackbar import Snackbar
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton
from plyer import notification as plyer_notification


class NotificationManager:
    """مدير الإشعارات"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        self.notifications = []
        self.callbacks = {}
        self.is_running = False
        self.check_interval = 60  # فحص كل دقيقة
        self._create_notifications_table()
        
    def _create_notifications_table(self):
        """إنشاء جدول الإشعارات"""
        try:
            self.db.cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    type TEXT DEFAULT 'info',
                    priority INTEGER DEFAULT 1,
                    is_read BOOLEAN DEFAULT 0,
                    is_sent BOOLEAN DEFAULT 0,
                    scheduled_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    user_id INTEGER,
                    data TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            self.db.conn.commit()
        except Exception as e:
            self.db.logger.error(f"Error creating notifications table: {e}")
    
    def add_notification(self, title: str, message: str, notification_type: str = "info",
                        priority: int = 1, scheduled_at: datetime = None, 
                        user_id: int = None, data: Dict = None) -> int:
        """إضافة إشعار جديد"""
        try:
            data_json = json.dumps(data) if data else None
            
            self.db.cursor.execute('''
                INSERT INTO notifications 
                (title, message, type, priority, scheduled_at, user_id, data)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (title, message, notification_type, priority, 
                  scheduled_at.isoformat() if scheduled_at else None, 
                  user_id, data_json))
            
            self.db.conn.commit()
            notification_id = self.db.cursor.lastrowid
            
            # إرسال فوري إذا لم يكن مجدولاً
            if not scheduled_at:
                self._send_notification(notification_id)
            
            return notification_id
            
        except Exception as e:
            self.db.logger.error(f"Error adding notification: {e}")
            return None
    
    def get_notifications(self, user_id: int = None, unread_only: bool = False) -> List[Dict]:
        """الحصول على الإشعارات"""
        try:
            query = "SELECT * FROM notifications WHERE 1=1"
            params = []
            
            if user_id:
                query += " AND (user_id = ? OR user_id IS NULL)"
                params.append(user_id)
            
            if unread_only:
                query += " AND is_read = 0"
            
            query += " ORDER BY priority DESC, created_at DESC"
            
            self.db.cursor.execute(query, params)
            notifications = [dict(row) for row in self.db.cursor.fetchall()]
            
            # تحويل البيانات JSON
            for notification in notifications:
                if notification['data']:
                    try:
                        notification['data'] = json.loads(notification['data'])
                    except:
                        notification['data'] = {}
                else:
                    notification['data'] = {}
            
            return notifications
            
        except Exception as e:
            self.db.logger.error(f"Error getting notifications: {e}")
            return []
    
    def mark_as_read(self, notification_id: int) -> bool:
        """تمييز الإشعار كمقروء"""
        try:
            self.db.cursor.execute(
                "UPDATE notifications SET is_read = 1 WHERE id = ?",
                (notification_id,)
            )
            self.db.conn.commit()
            return True
        except Exception as e:
            self.db.logger.error(f"Error marking notification as read: {e}")
            return False
    
    def mark_all_as_read(self, user_id: int = None) -> bool:
        """تمييز جميع الإشعارات كمقروءة"""
        try:
            query = "UPDATE notifications SET is_read = 1"
            params = []
            
            if user_id:
                query += " WHERE user_id = ? OR user_id IS NULL"
                params.append(user_id)
            
            self.db.cursor.execute(query, params)
            self.db.conn.commit()
            return True
        except Exception as e:
            self.db.logger.error(f"Error marking all notifications as read: {e}")
            return False
    
    def delete_notification(self, notification_id: int) -> bool:
        """حذف إشعار"""
        try:
            self.db.cursor.execute(
                "DELETE FROM notifications WHERE id = ?",
                (notification_id,)
            )
            self.db.conn.commit()
            return True
        except Exception as e:
            self.db.logger.error(f"Error deleting notification: {e}")
            return False
    
    def _send_notification(self, notification_id: int):
        """إرسال إشعار"""
        try:
            self.db.cursor.execute(
                "SELECT * FROM notifications WHERE id = ?",
                (notification_id,)
            )
            notification = self.db.cursor.fetchone()
            
            if not notification:
                return
            
            notification = dict(notification)
            
            # إرسال إشعار النظام
            self._send_system_notification(notification)
            
            # إرسال إشعار داخل التطبيق
            self._send_in_app_notification(notification)
            
            # تمييز كمرسل
            self.db.cursor.execute(
                "UPDATE notifications SET is_sent = 1 WHERE id = ?",
                (notification_id,)
            )
            self.db.conn.commit()
            
        except Exception as e:
            self.db.logger.error(f"Error sending notification: {e}")
    
    def _send_system_notification(self, notification: Dict):
        """إرسال إشعار النظام"""
        try:
            plyer_notification.notify(
                title=notification['title'],
                message=notification['message'],
                app_name="أداة مندوب المبيعات",
                timeout=10
            )
        except Exception as e:
            self.db.logger.error(f"Error sending system notification: {e}")
    
    def _send_in_app_notification(self, notification: Dict):
        """إرسال إشعار داخل التطبيق"""
        def show_notification(dt):
            try:
                # تحديد نوع الإشعار
                notification_types = {
                    'info': 'info',
                    'success': 'success',
                    'warning': 'warning',
                    'error': 'error'
                }
                
                notification_type = notification_types.get(
                    notification['type'], 'info'
                )
                
                # إنشاء Snackbar
                snackbar = Snackbar(
                    text=f"{notification['title']}: {notification['message']}",
                    duration=5
                )
                
                # تخصيص الألوان حسب النوع
                if notification_type == 'success':
                    snackbar.bg_color = (0.2, 0.8, 0.2, 1)
                elif notification_type == 'warning':
                    snackbar.bg_color = (1, 0.6, 0, 1)
                elif notification_type == 'error':
                    snackbar.bg_color = (0.8, 0.2, 0.2, 1)
                
                snackbar.open()
                
            except Exception as e:
                self.db.logger.error(f"Error showing in-app notification: {e}")
        
        # جدولة الإشعار في الخيط الرئيسي
        Clock.schedule_once(show_notification, 0)
    
    def start_monitoring(self):
        """بدء مراقبة الإشعارات المجدولة"""
        if self.is_running:
            return
        
        self.is_running = True
        
        def monitor_notifications():
            while self.is_running:
                try:
                    self._check_scheduled_notifications()
                    threading.Event().wait(self.check_interval)
                except Exception as e:
                    self.db.logger.error(f"Error in notification monitoring: {e}")
        
        thread = threading.Thread(target=monitor_notifications, daemon=True)
        thread.start()
    
    def stop_monitoring(self):
        """إيقاف مراقبة الإشعارات"""
        self.is_running = False
    
    def _check_scheduled_notifications(self):
        """فحص الإشعارات المجدولة"""
        try:
            now = datetime.now()
            
            self.db.cursor.execute('''
                SELECT id FROM notifications 
                WHERE scheduled_at <= ? AND is_sent = 0
            ''', (now.isoformat(),))
            
            notifications = self.db.cursor.fetchall()
            
            for notification in notifications:
                self._send_notification(notification['id'])
                
        except Exception as e:
            self.db.logger.error(f"Error checking scheduled notifications: {e}")
    
    def register_callback(self, event_type: str, callback: Callable):
        """تسجيل callback للأحداث"""
        if event_type not in self.callbacks:
            self.callbacks[event_type] = []
        self.callbacks[event_type].append(callback)
    
    def trigger_event(self, event_type: str, data: Dict = None):
        """تشغيل حدث"""
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    callback(data)
                except Exception as e:
                    self.db.logger.error(f"Error in callback: {e}")


class NotificationTemplates:
    """قوالب الإشعارات"""
    
    @staticmethod
    def new_order_notification(client_name: str, order_id: int) -> Dict:
        """إشعار طلب جديد"""
        return {
            'title': 'طلب جديد',
            'message': f'تم إنشاء طلب جديد من العميل {client_name}',
            'type': 'info',
            'priority': 2,
            'data': {'order_id': order_id, 'client_name': client_name}
        }
    
    @staticmethod
    def low_stock_notification(product_name: str, current_stock: int) -> Dict:
        """إشعار نفاد المخزون"""
        return {
            'title': 'تحذير مخزون منخفض',
            'message': f'المنتج {product_name} أوشك على النفاد (المتوفر: {current_stock})',
            'type': 'warning',
            'priority': 3,
            'data': {'product_name': product_name, 'current_stock': current_stock}
        }
    
    @staticmethod
    def payment_due_notification(client_name: str, amount: float) -> Dict:
        """إشعار استحقاق دفع"""
        return {
            'title': 'استحقاق دفع',
            'message': f'العميل {client_name} لديه مبلغ مستحق: {amount:.2f}',
            'type': 'warning',
            'priority': 2,
            'data': {'client_name': client_name, 'amount': amount}
        }
    
    @staticmethod
    def order_delivered_notification(order_id: int, client_name: str) -> Dict:
        """إشعار تسليم طلب"""
        return {
            'title': 'تم التسليم',
            'message': f'تم تسليم الطلب #{order_id} للعميل {client_name}',
            'type': 'success',
            'priority': 1,
            'data': {'order_id': order_id, 'client_name': client_name}
        }
    
    @staticmethod
    def backup_completed_notification() -> Dict:
        """إشعار اكتمال النسخ الاحتياطي"""
        return {
            'title': 'نسخ احتياطي',
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
            'type': 'success',
            'priority': 1,
            'data': {'backup_time': datetime.now().isoformat()}
        }
    
    @staticmethod
    def system_error_notification(error_message: str) -> Dict:
        """إشعار خطأ في النظام"""
        return {
            'title': 'خطأ في النظام',
            'message': f'حدث خطأ: {error_message}',
            'type': 'error',
            'priority': 4,
            'data': {'error_message': error_message, 'timestamp': datetime.now().isoformat()}
        }


class AutoNotifications:
    """الإشعارات التلقائية"""
    
    def __init__(self, notification_manager, db_manager):
        self.notification_manager = notification_manager
        self.db = db_manager
        
    def setup_auto_notifications(self):
        """إعداد الإشعارات التلقائية"""
        # إشعارات يومية
        self._schedule_daily_summary()
        
        # إشعارات أسبوعية
        self._schedule_weekly_report()
        
        # فحص المخزون المنخفض
        self._check_low_stock()
        
        # فحص المدفوعات المستحقة
        self._check_due_payments()
    
    def _schedule_daily_summary(self):
        """جدولة ملخص يومي"""
        tomorrow = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0) + timedelta(days=1)
        
        self.notification_manager.add_notification(
            title="ملخص يومي",
            message="ملخص أنشطة اليوم متاح الآن",
            notification_type="info",
            scheduled_at=tomorrow
        )
    
    def _schedule_weekly_report(self):
        """جدولة تقرير أسبوعي"""
        # حساب الاثنين القادم
        days_ahead = 0 - datetime.now().weekday()  # 0 = الاثنين
        if days_ahead <= 0:
            days_ahead += 7
        
        next_monday = datetime.now() + timedelta(days=days_ahead)
        next_monday = next_monday.replace(hour=10, minute=0, second=0, microsecond=0)
        
        self.notification_manager.add_notification(
            title="تقرير أسبوعي",
            message="التقرير الأسبوعي جاهز للمراجعة",
            notification_type="info",
            scheduled_at=next_monday
        )
    
    def _check_low_stock(self):
        """فحص المخزون المنخفض"""
        try:
            self.db.cursor.execute('''
                SELECT name, stock_quantity, min_stock_level
                FROM products
                WHERE stock_quantity <= min_stock_level AND is_active = 1
            ''')
            
            low_stock_products = self.db.cursor.fetchall()
            
            for product in low_stock_products:
                notification = NotificationTemplates.low_stock_notification(
                    product['name'], product['stock_quantity']
                )
                self.notification_manager.add_notification(**notification)
                
        except Exception as e:
            self.db.logger.error(f"Error checking low stock: {e}")
    
    def _check_due_payments(self):
        """فحص المدفوعات المستحقة"""
        try:
            self.db.cursor.execute('''
                SELECT name, current_balance
                FROM clients
                WHERE current_balance > 0
            ''')
            
            clients_with_balance = self.db.cursor.fetchall()
            
            for client in clients_with_balance:
                notification = NotificationTemplates.payment_due_notification(
                    client['name'], client['current_balance']
                )
                self.notification_manager.add_notification(**notification)
                
        except Exception as e:
            self.db.logger.error(f"Error checking due payments: {e}")
