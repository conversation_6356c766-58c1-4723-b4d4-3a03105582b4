"""
نظام التقارير المتقدم
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors
from reportlab.lib.units import inch


class ReportGenerator:
    """مولد التقارير"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        
    def get_sales_summary(self, start_date: str = None, end_date: str = None) -> Dict:
        """ملخص المبيعات"""
        try:
            query = """
                SELECT 
                    COUNT(*) as total_orders,
                    SUM(total_price) as total_revenue,
                    AVG(total_price) as avg_order_value,
                    COUNT(DISTINCT client_id) as unique_clients
                FROM orders
                WHERE 1=1
            """
            params = []
            
            if start_date:
                query += " AND created_at >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND created_at <= ?"
                params.append(end_date)
            
            self.db.cursor.execute(query, params)
            result = self.db.cursor.fetchone()
            
            return {
                'total_orders': result['total_orders'] or 0,
                'total_revenue': float(result['total_revenue'] or 0),
                'avg_order_value': float(result['avg_order_value'] or 0),
                'unique_clients': result['unique_clients'] or 0
            }
            
        except Exception as e:
            self.db.logger.error(f"Error generating sales summary: {e}")
            return {}
    
    def get_top_clients(self, limit: int = 10, start_date: str = None, end_date: str = None) -> List[Dict]:
        """أفضل العملاء"""
        try:
            query = """
                SELECT 
                    c.name,
                    c.phone,
                    COUNT(o.id) as order_count,
                    SUM(o.total_price) as total_spent,
                    AVG(o.total_price) as avg_order_value,
                    MAX(o.created_at) as last_order_date
                FROM clients c
                JOIN orders o ON c.id = o.client_id
                WHERE 1=1
            """
            params = []
            
            if start_date:
                query += " AND o.created_at >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND o.created_at <= ?"
                params.append(end_date)
            
            query += """
                GROUP BY c.id, c.name, c.phone
                ORDER BY total_spent DESC
                LIMIT ?
            """
            params.append(limit)
            
            self.db.cursor.execute(query, params)
            return [dict(row) for row in self.db.cursor.fetchall()]
            
        except Exception as e:
            self.db.logger.error(f"Error getting top clients: {e}")
            return []
    
    def get_category_performance(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """أداء الفئات"""
        try:
            query = """
                SELECT 
                    cat.name as category_name,
                    COUNT(o.id) as order_count,
                    SUM(o.total_price) as total_revenue,
                    AVG(o.total_price) as avg_order_value,
                    SUM(o.quantity) as total_quantity
                FROM categories cat
                JOIN orders o ON cat.id = o.category_id
                WHERE 1=1
            """
            params = []
            
            if start_date:
                query += " AND o.created_at >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND o.created_at <= ?"
                params.append(end_date)
            
            query += """
                GROUP BY cat.id, cat.name
                ORDER BY total_revenue DESC
            """
            
            self.db.cursor.execute(query, params)
            return [dict(row) for row in self.db.cursor.fetchall()]
            
        except Exception as e:
            self.db.logger.error(f"Error getting category performance: {e}")
            return []
    
    def get_daily_sales_trend(self, days: int = 30) -> List[Dict]:
        """اتجاه المبيعات اليومية"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            query = """
                SELECT 
                    DATE(created_at) as sale_date,
                    COUNT(*) as order_count,
                    SUM(total_price) as daily_revenue,
                    AVG(total_price) as avg_order_value
                FROM orders
                WHERE created_at >= ? AND created_at <= ?
                GROUP BY DATE(created_at)
                ORDER BY sale_date
            """
            
            self.db.cursor.execute(query, (start_date.isoformat(), end_date.isoformat()))
            return [dict(row) for row in self.db.cursor.fetchall()]
            
        except Exception as e:
            self.db.logger.error(f"Error getting daily sales trend: {e}")
            return []
    
    def get_order_status_distribution(self) -> List[Dict]:
        """توزيع حالات الطلبات"""
        try:
            query = """
                SELECT 
                    status,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM orders), 2) as percentage
                FROM orders
                GROUP BY status
                ORDER BY count DESC
            """
            
            self.db.cursor.execute(query)
            return [dict(row) for row in self.db.cursor.fetchall()]
            
        except Exception as e:
            self.db.logger.error(f"Error getting order status distribution: {e}")
            return []
    
    def generate_pdf_report(self, report_data: Dict, filename: str) -> bool:
        """إنشاء تقرير PDF"""
        try:
            doc = SimpleDocTemplate(filename, pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # عنوان التقرير
            title = Paragraph("تقرير المبيعات", styles['Title'])
            story.append(title)
            story.append(Spacer(1, 12))
            
            # ملخص المبيعات
            if 'sales_summary' in report_data:
                summary = report_data['sales_summary']
                summary_data = [
                    ['المؤشر', 'القيمة'],
                    ['إجمالي الطلبات', str(summary.get('total_orders', 0))],
                    ['إجمالي الإيرادات', f"{summary.get('total_revenue', 0):.2f}"],
                    ['متوسط قيمة الطلب', f"{summary.get('avg_order_value', 0):.2f}"],
                    ['عدد العملاء الفريدين', str(summary.get('unique_clients', 0))]
                ]
                
                summary_table = Table(summary_data)
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(Paragraph("ملخص المبيعات", styles['Heading2']))
                story.append(summary_table)
                story.append(Spacer(1, 12))
            
            # أفضل العملاء
            if 'top_clients' in report_data:
                clients = report_data['top_clients']
                if clients:
                    clients_data = [['اسم العميل', 'عدد الطلبات', 'إجمالي الإنفاق']]
                    for client in clients[:10]:
                        clients_data.append([
                            client['name'],
                            str(client['order_count']),
                            f"{client['total_spent']:.2f}"
                        ])
                    
                    clients_table = Table(clients_data)
                    clients_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    
                    story.append(Paragraph("أفضل العملاء", styles['Heading2']))
                    story.append(clients_table)
                    story.append(Spacer(1, 12))
            
            # بناء المستند
            doc.build(story)
            return True
            
        except Exception as e:
            self.db.logger.error(f"Error generating PDF report: {e}")
            return False
    
    def export_to_excel(self, report_data: Dict, filename: str) -> bool:
        """تصدير التقرير إلى Excel"""
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                
                # ملخص المبيعات
                if 'sales_summary' in report_data:
                    summary_df = pd.DataFrame([report_data['sales_summary']])
                    summary_df.to_excel(writer, sheet_name='ملخص المبيعات', index=False)
                
                # أفضل العملاء
                if 'top_clients' in report_data:
                    clients_df = pd.DataFrame(report_data['top_clients'])
                    clients_df.to_excel(writer, sheet_name='أفضل العملاء', index=False)
                
                # أداء الفئات
                if 'category_performance' in report_data:
                    categories_df = pd.DataFrame(report_data['category_performance'])
                    categories_df.to_excel(writer, sheet_name='أداء الفئات', index=False)
                
                # اتجاه المبيعات
                if 'daily_sales' in report_data:
                    sales_df = pd.DataFrame(report_data['daily_sales'])
                    sales_df.to_excel(writer, sheet_name='اتجاه المبيعات', index=False)
            
            return True
            
        except Exception as e:
            self.db.logger.error(f"Error exporting to Excel: {e}")
            return False
    
    def generate_comprehensive_report(self, start_date: str = None, end_date: str = None) -> Dict:
        """إنشاء تقرير شامل"""
        report_data = {
            'generated_at': datetime.now().isoformat(),
            'period': {
                'start_date': start_date,
                'end_date': end_date
            },
            'sales_summary': self.get_sales_summary(start_date, end_date),
            'top_clients': self.get_top_clients(10, start_date, end_date),
            'category_performance': self.get_category_performance(start_date, end_date),
            'daily_sales': self.get_daily_sales_trend(30),
            'order_status': self.get_order_status_distribution()
        }
        
        return report_data


class ChartGenerator:
    """مولد الرسوم البيانية"""
    
    @staticmethod
    def create_sales_trend_chart(data: List[Dict], filename: str = None) -> str:
        """رسم بياني لاتجاه المبيعات"""
        try:
            if not data:
                return None
            
            df = pd.DataFrame(data)
            df['sale_date'] = pd.to_datetime(df['sale_date'])
            
            plt.figure(figsize=(12, 6))
            plt.plot(df['sale_date'], df['daily_revenue'], marker='o', linewidth=2)
            plt.title('اتجاه المبيعات اليومية', fontsize=16, fontweight='bold')
            plt.xlabel('التاريخ')
            plt.ylabel('الإيرادات')
            plt.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            
            # تنسيق التواريخ
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=5))
            
            plt.tight_layout()
            
            if filename:
                plt.savefig(filename, dpi=300, bbox_inches='tight')
                plt.close()
                return filename
            else:
                plt.show()
                return None
                
        except Exception as e:
            print(f"Error creating sales trend chart: {e}")
            return None
    
    @staticmethod
    def create_category_pie_chart(data: List[Dict], filename: str = None) -> str:
        """رسم بياني دائري لأداء الفئات"""
        try:
            if not data:
                return None
            
            df = pd.DataFrame(data)
            
            plt.figure(figsize=(10, 8))
            plt.pie(df['total_revenue'], labels=df['category_name'], autopct='%1.1f%%')
            plt.title('توزيع الإيرادات حسب الفئة', fontsize=16, fontweight='bold')
            
            if filename:
                plt.savefig(filename, dpi=300, bbox_inches='tight')
                plt.close()
                return filename
            else:
                plt.show()
                return None
                
        except Exception as e:
            print(f"Error creating category pie chart: {e}")
            return None
