# Sales Representative Tool

A high-performance mobile app built with Python and KivyMD for sales representatives to manage clients and orders.

## Features

- Splash Screen with smooth transitions
- Login Screen (Mock authentication)
- Client Management
  - View list of clients
  - Add new clients
  - Real-time search with debouncing
- Order Management
  - View orders by client
  - Create new orders
  - Optimized search functionality
- Category Management
  - Add/view product categories
  - Edit/delete categories
  - Fast search with database optimization
- Profile Screen
  - View agent information
  - Logout functionality

## Performance Optimizations

- Database singleton pattern for efficient connections
- SQLite indexing for faster queries
- LRU caching for frequently accessed data
- Debounced search to reduce database load
- Optimized database queries with proper SQL patterns
- Loading indicators for better user experience
- Asynchronous data loading

## Requirements

- Python 3.7+
- KivyMD
- SQLite

## Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/sales-rep-tool.git
cd sales-rep-tool
```

2. Create a virtual environment (optional but recommended):
```
python -m venv venv
```

3. Activate the virtual environment:
   - On Windows:
   ```
   venv\Scripts\activate
   ```
   - On macOS/Linux:
   ```
   source venv/bin/activate
   ```

4. Install dependencies:
```
pip install -r requirements.txt
```

## Running the App

```
python main.py
```

## Building for Android

To build the app for Android, you'll need to use Buildozer:

1. Install Buildozer:
```
pip install buildozer
```

2. Initialize Buildozer (if not already done):
```
buildozer init
```

3. Edit the buildozer.spec file as needed

4. Build the APK:
```
buildozer -v android debug
```

## Usage

1. Login with any username and password
2. Navigate using the bottom navigation bar
3. Add clients, categories, and orders as needed

## Database

The app uses SQLite for data storage. The database file is created automatically when the app runs for the first time.

## License

MIT 