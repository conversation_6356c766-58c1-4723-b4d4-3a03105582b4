import time
from functools import wraps
from threading import Timer

def debounce(wait_time):
    """
    Decorator that will postpone a function's execution until after wait_time seconds
    have elapsed since the last time it was invoked.
    
    Args:
        wait_time (float): Time to wait in seconds
    """
    def decorator(fn):
        timer = None
        
        @wraps(fn)
        def debounced(*args, **kwargs):
            nonlocal timer
            
            def call_function():
                fn(*args, **kwargs)
            
            if timer is not None:
                timer.cancel()
            
            timer = Timer(wait_time, call_function)
            timer.start()
            
        return debounced
    
    return decorator 