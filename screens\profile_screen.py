from kivymd.uix.screen import MDScreen
from kivy.app import App
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton
from kivymd.uix.snackbar import Snackbar

class ProfileScreen(MDScreen):
    """
    شاشة الملف الشخصي للمستخدم
    تتيح للمستخدم تعديل إعدادات التطبيق وتسجيل الخروج
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = App.get_running_app()
        self.logout_dialog = None
    
    def on_enter(self):
        """يتم استدعاء هذه الدالة عند الدخول إلى الشاشة"""
        # يمكن تحديث بيانات الملف الشخصي هنا إذا لزم الأمر
        pass
    
    def toggle_theme(self, switch_instance):
        """تبديل بين الوضع الفاتح والداكن"""
        # تم نقل هذه الوظيفة إلى الدالة toggle_theme_style في الملف الرئيسي
        pass
    
    def logout(self):
        """عرض مربع حوار تأكيد تسجيل الخروج"""
        if not self.logout_dialog:
            self.logout_dialog = MDDialog(
                title="تأكيد تسجيل الخروج",
                text="هل أنت متأكد من رغبتك في تسجيل الخروج؟",
                buttons=[
                    MDFlatButton(
                        text="إلغاء",
                        on_release=self.dismiss_logout_dialog
                    ),
                    MDFlatButton(
                        text="تأكيد",
                        on_release=self.confirm_logout
                    ),
                ],
            )
        self.logout_dialog.open()
    
    def dismiss_logout_dialog(self, *args):
        """إغلاق مربع حوار تسجيل الخروج"""
        if self.logout_dialog:
            self.logout_dialog.dismiss()
    
    def confirm_logout(self, *args):
        """تأكيد تسجيل الخروج والعودة إلى شاشة تسجيل الدخول"""
        self.dismiss_logout_dialog()
        # حفظ أي بيانات ضرورية قبل تسجيل الخروج
        self.app.save_user_preferences()
        # الانتقال إلى شاشة تسجيل الدخول
        self.app.root.ids.screen_manager.current = 'login'
        # عرض رسالة تأكيد
        Snackbar(text="تم تسجيل الخروج بنجاح").open()