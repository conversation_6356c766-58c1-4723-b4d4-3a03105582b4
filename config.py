"""
إعدادات التطبيق
"""

import os
from typing import Dict, Any


class AppConfig:
    """إعدادات التطبيق"""
    
    # إعدادات قاعدة البيانات
    DATABASE_NAME = "sales_rep.db"
    DATABASE_TIMEOUT = 30.0
    
    # إعدادات الأمان
    SESSION_TIMEOUT_HOURS = 24
    MAX_FAILED_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 5
    
    # إعدادات كلمة المرور
    MIN_PASSWORD_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGITS = True
    REQUIRE_SPECIAL_CHARS = True
    
    # إعدادات التطبيق
    APP_NAME = "أداة مندوب المبيعات"
    APP_VERSION = "2.0.0"
    
    # إعدادات النسخ الاحتياطي
    AUTO_BACKUP_ENABLED = True
    BACKUP_INTERVAL_HOURS = 24
    MAX_BACKUP_FILES = 7
    
    # إعدادات التسجيل
    LOG_LEVEL = "INFO"
    LOG_FILE = "sales_rep.log"
    MAX_LOG_SIZE_MB = 10
    
    # إعدادات الواجهة
    DEFAULT_THEME = "Light"
    DEFAULT_PRIMARY_COLOR = "Blue"
    DEFAULT_ACCENT_COLOR = "Amber"
    
    # إعدادات التصدير
    EXPORT_FORMATS = ["xlsx", "csv", "pdf"]
    DEFAULT_EXPORT_PATH = "exports"
    
    # إعدادات الأداء
    CACHE_SIZE = 100
    DEBOUNCE_DELAY = 0.3
    
    @classmethod
    def get_all_settings(cls) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات"""
        settings = {}
        for attr in dir(cls):
            if not attr.startswith('_') and not callable(getattr(cls, attr)):
                if attr != 'get_all_settings':
                    settings[attr] = getattr(cls, attr)
        return settings
    
    @classmethod
    def update_setting(cls, key: str, value: Any) -> bool:
        """تحديث إعداد معين"""
        if hasattr(cls, key):
            setattr(cls, key, value)
            return True
        return False


class DatabaseConfig:
    """إعدادات قاعدة البيانات المتقدمة"""
    
    # إعدادات الأداء
    PRAGMA_SETTINGS = {
        "journal_mode": "WAL",
        "synchronous": "NORMAL",
        "cache_size": 10000,
        "temp_store": "MEMORY",
        "mmap_size": 268435456,  # 256MB
        "foreign_keys": "ON"
    }
    
    # إعدادات الفهرسة
    INDEXES = [
        ("idx_client_name", "clients", "name"),
        ("idx_client_phone", "clients", "phone"),
        ("idx_category_name", "categories", "name"),
        ("idx_order_client", "orders", "client_id"),
        ("idx_order_category", "orders", "category_id"),
        ("idx_order_type", "orders", "order_type"),
        ("idx_order_date", "orders", "created_at"),
        ("idx_user_username", "users", "username"),
        ("idx_user_email", "users", "email")
    ]


class SecurityConfig:
    """إعدادات الأمان المتقدمة"""
    
    # إعدادات التشفير
    ENCRYPTION_ALGORITHM = "Fernet"
    KEY_DERIVATION_ITERATIONS = 100000
    
    # إعدادات الجلسات
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = "Strict"
    
    # إعدادات التدقيق
    AUDIT_LOGIN_ATTEMPTS = True
    AUDIT_DATA_CHANGES = True
    AUDIT_EXPORT_OPERATIONS = True
    
    # إعدادات الحماية
    RATE_LIMITING_ENABLED = True
    MAX_REQUESTS_PER_MINUTE = 60
    
    # قائمة الأحداث المسجلة
    LOGGED_EVENTS = [
        "user_login",
        "user_logout",
        "failed_login",
        "account_locked",
        "password_changed",
        "data_export",
        "backup_created",
        "backup_restored"
    ]


class UIConfig:
    """إعدادات واجهة المستخدم"""
    
    # إعدادات النافذة
    WINDOW_SIZE = (400, 700)
    WINDOW_TITLE = "أداة مندوب المبيعات"
    
    # إعدادات الألوان
    THEME_COLORS = {
        "primary_palettes": [
            "Red", "Pink", "Purple", "DeepPurple", "Indigo",
            "Blue", "LightBlue", "Cyan", "Teal", "Green",
            "LightGreen", "Lime", "Yellow", "Amber", "Orange",
            "DeepOrange", "Brown", "Gray", "BlueGray"
        ],
        "accent_palettes": [
            "Red", "Pink", "Purple", "DeepPurple", "Indigo",
            "Blue", "LightBlue", "Cyan", "Teal", "Green",
            "LightGreen", "Lime", "Yellow", "Amber", "Orange"
        ]
    }
    
    # إعدادات الرسوم المتحركة
    ANIMATION_DURATION = 0.3
    TRANSITION_TYPE = "FadeTransition"
    
    # إعدادات القوائم
    LIST_ITEM_HEIGHT = 72
    SEARCH_DEBOUNCE_DELAY = 0.3
    
    # إعدادات الحوارات
    DIALOG_AUTO_DISMISS = True
    SNACKBAR_DURATION = 3


class ExportConfig:
    """إعدادات التصدير"""
    
    # تنسيقات التصدير المدعومة
    SUPPORTED_FORMATS = {
        "xlsx": {
            "name": "Excel Workbook",
            "extension": ".xlsx",
            "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        },
        "csv": {
            "name": "Comma Separated Values",
            "extension": ".csv",
            "mime_type": "text/csv"
        },
        "pdf": {
            "name": "Portable Document Format",
            "extension": ".pdf",
            "mime_type": "application/pdf"
        }
    }
    
    # إعدادات Excel
    EXCEL_SETTINGS = {
        "engine": "openpyxl",
        "index": False,
        "header": True
    }
    
    # إعدادات CSV
    CSV_SETTINGS = {
        "encoding": "utf-8-sig",
        "index": False,
        "sep": ","
    }
    
    # إعدادات PDF
    PDF_SETTINGS = {
        "page_size": "A4",
        "orientation": "portrait",
        "font_size": 10
    }


# إنشاء مثيل عام للإعدادات
app_config = AppConfig()
db_config = DatabaseConfig()
security_config = SecurityConfig()
ui_config = UIConfig()
export_config = ExportConfig()
