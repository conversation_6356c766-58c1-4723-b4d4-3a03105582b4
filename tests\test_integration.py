"""
اختبارات التكامل
"""

import pytest
import tempfile
import os
import json
from datetime import datetime, timedelta

from database import DatabaseManager
from security import UserManager
from reports import ReportGenerator
from notifications import NotificationManager
from performance import MemoryManager, PerformanceMonitor


class TestFullIntegration:
    """اختبارات التكامل الكاملة"""
    
    def setup_method(self):
        """إعداد الاختبار"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # إنشاء قاعدة البيانات
        self.db = DatabaseManager(self.temp_db.name)
        
        # إنشاء مدير المستخدمين
        self.user_manager = UserManager(self.db)
        
        # إنشاء مولد التقارير
        self.report_generator = ReportGenerator(self.db)
        
        # إنشاء مدير الإشعارات
        self.notification_manager = NotificationManager(self.db)
        
        # إضافة بيانات تجريبية
        self._setup_test_data()
    
    def teardown_method(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        os.unlink(self.temp_db.name)
    
    def _setup_test_data(self):
        """إعداد البيانات التجريبية"""
        # إنشاء مستخدم
        self.user_manager.create_user(
            "test_user", "TestPass123!", 
            "<EMAIL>", "Test User"
        )
        
        # إضافة عملاء
        self.client1_id = self.db.add_client(
            "أحمد محمد", "0501234567", 
            "الرياض - حي النزهة", "<EMAIL>"
        )
        self.client2_id = self.db.add_client(
            "سارة عبدالله", "0551234567", 
            "جدة - حي الروضة", "<EMAIL>"
        )
        
        # إضافة فئات
        self.category1_id = self.db.add_category("إلكترونيات", "منتجات إلكترونية")
        self.category2_id = self.db.add_category("ملابس", "ملابس متنوعة")
        
        # إضافة طلبات
        self.order1_id = self.db.add_order(
            self.client1_id, self.category1_id, 
            "جهاز كمبيوتر", "كبير", 1, "توصيل"
        )
        self.order2_id = self.db.add_order(
            self.client2_id, self.category2_id, 
            "قميص", "متوسط", 2, "استلام"
        )
    
    def test_user_authentication_flow(self):
        """اختبار تدفق المصادقة"""
        # مصادقة صحيحة
        success, message, user_data = self.user_manager.authenticate_user(
            "test_user", "TestPass123!"
        )
        assert success
        assert user_data is not None
        assert user_data['username'] == "test_user"
        
        # مصادقة خاطئة
        success, message, user_data = self.user_manager.authenticate_user(
            "test_user", "wrong_password"
        )
        assert not success
        assert user_data is None
    
    def test_data_flow_integrity(self):
        """اختبار تكامل تدفق البيانات"""
        # التحقق من العملاء
        clients = self.db.get_all_clients()
        assert len(clients) == 2
        
        # التحقق من الفئات
        categories = self.db.get_all_categories()
        assert len(categories) == 2
        
        # التحقق من الطلبات
        orders = self.db.get_all_orders()
        assert len(orders) == 2
        
        # التحقق من العلاقات
        client_orders = self.db.get_client_orders(self.client1_id)
        assert len(client_orders) == 1
        assert client_orders[0]['client_name'] == "أحمد محمد"
    
    def test_search_functionality(self):
        """اختبار وظائف البحث"""
        # البحث في العملاء
        search_results = self.db.search_clients("أحمد")
        assert len(search_results) == 1
        assert search_results[0]['name'] == "أحمد محمد"
        
        # البحث في الفئات
        search_results = self.db.search_categories("إلكترونيات")
        assert len(search_results) == 1
        assert search_results[0]['name'] == "إلكترونيات"
        
        # البحث في الطلبات
        search_results = self.db.search_orders("جهاز")
        assert len(search_results) == 1
        assert search_results[0]['order_type'] == "جهاز كمبيوتر"
    
    def test_reports_generation(self):
        """اختبار إنشاء التقارير"""
        # ملخص المبيعات
        summary = self.report_generator.get_sales_summary()
        assert summary['total_orders'] == 2
        assert summary['unique_clients'] == 2
        
        # أفضل العملاء
        top_clients = self.report_generator.get_top_clients(5)
        assert len(top_clients) == 2
        
        # أداء الفئات
        category_performance = self.report_generator.get_category_performance()
        assert len(category_performance) == 2
        
        # التقرير الشامل
        comprehensive_report = self.report_generator.generate_comprehensive_report()
        assert 'sales_summary' in comprehensive_report
        assert 'top_clients' in comprehensive_report
        assert 'category_performance' in comprehensive_report
    
    def test_notifications_system(self):
        """اختبار نظام الإشعارات"""
        # إضافة إشعار
        notification_id = self.notification_manager.add_notification(
            "اختبار التكامل",
            "هذا إشعار اختبار التكامل",
            "info"
        )
        assert notification_id is not None
        
        # الحصول على الإشعارات
        notifications = self.notification_manager.get_notifications()
        assert len(notifications) >= 1
        
        # تمييز كمقروء
        success = self.notification_manager.mark_as_read(notification_id)
        assert success
        
        # التحقق من التحديث
        notifications = self.notification_manager.get_notifications(unread_only=True)
        unread_count = len([n for n in notifications if not n['is_read']])
        assert unread_count == 0
    
    def test_database_backup_restore(self):
        """اختبار النسخ الاحتياطي والاستعادة"""
        # إنشاء نسخة احتياطية
        backup_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        backup_file.close()
        
        success = self.db.backup_database(backup_file.name)
        assert success
        assert os.path.exists(backup_file.name)
        
        # التحقق من حجم الملف
        assert os.path.getsize(backup_file.name) > 0
        
        # تنظيف
        os.unlink(backup_file.name)
    
    def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        # محاولة إضافة عميل بمعرف غير صحيح
        result = self.db.add_client("", "", "")  # بيانات فارغة
        # يجب أن يعيد None أو يرفع استثناء
        
        # محاولة الحصول على عميل غير موجود
        client = self.db.get_client_by_id(99999)
        assert client is None
        
        # محاولة مصادقة بمستخدم غير موجود
        success, message, user_data = self.user_manager.authenticate_user(
            "nonexistent_user", "password"
        )
        assert not success
        assert user_data is None
    
    def test_performance_monitoring(self):
        """اختبار مراقبة الأداء"""
        memory_manager = MemoryManager()
        performance_monitor = PerformanceMonitor()
        
        # اختبار معلومات الذاكرة
        memory_info = memory_manager.get_memory_info()
        assert 'total_memory' in memory_info
        assert 'memory_percent' in memory_info
        
        # اختبار قياس الأداء
        @performance_monitor.measure_time("test_operation")
        def test_operation():
            # عملية بسيطة للاختبار
            return sum(range(1000))
        
        result = test_operation()
        assert result == 499500
        
        # التحقق من تسجيل المقاييس
        metrics = performance_monitor.get_metrics_summary()
        assert "test_operation" in metrics
    
    def test_data_validation(self):
        """اختبار التحقق من صحة البيانات"""
        from screens.validation import validate_client_form, validate_order_form
        
        # اختبار التحقق من بيانات العميل
        is_valid, message = validate_client_form("", "123456789", "العنوان", "<EMAIL>")
        assert not is_valid  # الاسم فارغ
        
        is_valid, message = validate_client_form("اسم صحيح", "123456789", "العنوان", "<EMAIL>")
        assert is_valid
        
        # اختبار التحقق من بيانات الطلب
        is_valid, message = validate_order_form(None, self.category1_id, "منتج", "كبير", "2", "توصيل")
        assert not is_valid  # العميل غير محدد
        
        is_valid, message = validate_order_form(self.client1_id, self.category1_id, "منتج", "كبير", "2", "توصيل")
        assert is_valid
    
    def test_concurrent_operations(self):
        """اختبار العمليات المتزامنة"""
        import threading
        import time
        
        results = []
        errors = []
        
        def add_client_worker(index):
            try:
                client_id = self.db.add_client(
                    f"عميل {index}",
                    f"05012345{index:02d}",
                    f"عنوان {index}",
                    f"client{index}@test.com"
                )
                results.append(client_id)
            except Exception as e:
                errors.append(str(e))
        
        # إنشاء عدة threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=add_client_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الـ threads
        for thread in threads:
            thread.join()
        
        # التحقق من النتائج
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 5
        assert all(result is not None for result in results)
        
        # التحقق من إضافة العملاء
        all_clients = self.db.get_all_clients()
        assert len(all_clients) >= 7  # 2 أصلي + 5 جديد


class TestEdgeCases:
    """اختبار الحالات الحدية"""
    
    def setup_method(self):
        """إعداد الاختبار"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DatabaseManager(self.temp_db.name)
    
    def teardown_method(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        os.unlink(self.temp_db.name)
    
    def test_empty_database(self):
        """اختبار قاعدة بيانات فارغة"""
        # التحقق من الجداول الفارغة
        clients = self.db.get_all_clients()
        assert len(clients) == 0
        
        categories = self.db.get_all_categories()
        assert len(categories) == 0
        
        orders = self.db.get_all_orders()
        assert len(orders) == 0
    
    def test_large_data_sets(self):
        """اختبار مجموعات البيانات الكبيرة"""
        # إضافة عدد كبير من العملاء
        client_ids = []
        for i in range(100):
            client_id = self.db.add_client(
                f"عميل {i}",
                f"05012345{i:03d}",
                f"عنوان {i}",
                f"client{i}@test.com"
            )
            client_ids.append(client_id)
        
        # التحقق من الإضافة
        all_clients = self.db.get_all_clients()
        assert len(all_clients) == 100
        
        # اختبار البحث في البيانات الكبيرة
        search_results = self.db.search_clients("عميل 5")
        assert len(search_results) >= 10  # عميل 5, 50, 51, 52, ...
    
    def test_unicode_and_special_characters(self):
        """اختبار الأحرف الخاصة و Unicode"""
        # إضافة عميل بأحرف خاصة
        client_id = self.db.add_client(
            "أحمد بن محمد آل سعود",
            "+966-50-123-4567",
            "الرياض - حي الملك فهد - شارع الأمير محمد بن عبدالعزيز",
            "<EMAIL>"
        )
        assert client_id is not None
        
        # التحقق من الاسترداد
        client = self.db.get_client_by_id(client_id)
        assert client['name'] == "أحمد بن محمد آل سعود"
        
        # اختبار البحث بالأحرف العربية
        search_results = self.db.search_clients("أحمد")
        assert len(search_results) == 1
        assert search_results[0]['name'] == "أحمد بن محمد آل سعود"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
