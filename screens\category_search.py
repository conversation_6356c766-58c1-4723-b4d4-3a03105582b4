from kivymd.uix.list import TwoLineListItem, OneLineListItem

def search_categories(categories_list_widget, search_text, db=None):
    """
    Search categories based on search text and update the categories list widget
    
    Args:
        categories_list_widget: The MDList widget to update
        search_text: Text to search for in category names or descriptions
        db: Database manager instance
    """
    # Clear the current list
    categories_list_widget.clear_widgets()
    
    if not search_text.strip():
        # If search text is empty, show all categories
        categories = db.get_all_categories()
    else:
        # Use optimized database search
        categories = db.search_categories(search_text)
    
    if not categories:
        categories_list_widget.add_widget(OneLineListItem(text="No categories found"))
        return
    
    # Add categories to the list
    for category in categories:
        item = TwoLineListItem(
            text=category['name'],
            secondary_text=category['description'] if category['description'] else "No description",
            on_release=lambda x, cat_id=category['id']: x.parent.parent.parent.parent.show_category_dialog(cat_id)
        )
        categories_list_widget.add_widget(item) 