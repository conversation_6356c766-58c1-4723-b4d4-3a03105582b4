from kivymd.uix.screen import MDScreen
from kivy.clock import Clock
from kivy.properties import StringProperty
from kivy.animation import Animation

class SplashScreen(MDScreen):
    """
    A splash screen that shows a logo and app name
    before transitioning to the login screen
    """
    app_name = StringProperty("Sales Representative Tool")
    
    def on_enter(self):
        """Called when the screen is displayed"""
        # Schedule transition to login screen after 2 seconds
        Clock.schedule_once(self.start_transition, 2)
    
    def start_transition(self, dt):
        """Start the transition animation to the login screen"""
        # Create fade out animation
        animation = Animation(opacity=0, duration=0.5)
        
        # When animation completes, switch to login screen
        animation.bind(on_complete=self.switch_to_login)
        
        # Start animation on the splash screen
        animation.start(self)
    
    def switch_to_login(self, animation, widget):
        """Switch to the login screen"""
        self.manager.current = 'login'
        
        # Reset opacity for next time
        self.opacity = 1 