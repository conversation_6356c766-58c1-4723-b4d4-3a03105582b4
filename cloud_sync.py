"""
نظام المزامنة السحابية
"""

import json
import hashlib
import threading
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sqlite3
import os


class CloudSyncManager:
    """مدير المزامنة السحابية"""
    
    def __init__(self, db_manager, api_base_url: str = None, api_key: str = None):
        self.db = db_manager
        self.api_base_url = api_base_url or "https://api.salesrep.example.com"
        self.api_key = api_key
        self.sync_interval = 300  # 5 دقائق
        self.is_syncing = False
        self.last_sync = None
        self.sync_enabled = False
        self._create_sync_tables()
        
    def _create_sync_tables(self):
        """إنشاء جداول المزامنة"""
        try:
            # جدول حالة المزامنة
            self.db.cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_name TEXT NOT NULL,
                    record_id INTEGER NOT NULL,
                    local_hash TEXT,
                    remote_hash TEXT,
                    last_sync TIMESTAMP,
                    sync_status TEXT DEFAULT 'pending',
                    conflict_data TEXT,
                    UNIQUE(table_name, record_id)
                )
            ''')
            
            # جدول سجل المزامنة
            self.db.cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    operation TEXT NOT NULL,
                    table_name TEXT,
                    record_id INTEGER,
                    status TEXT NOT NULL,
                    message TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول إعدادات المزامنة
            self.db.cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            self.db.conn.commit()
            
        except Exception as e:
            self.db.logger.error(f"Error creating sync tables: {e}")
    
    def configure_sync(self, api_base_url: str, api_key: str, auto_sync: bool = True):
        """تكوين المزامنة"""
        try:
            self.api_base_url = api_base_url
            self.api_key = api_key
            self.sync_enabled = auto_sync
            
            # حفظ الإعدادات
            settings = {
                'api_base_url': api_base_url,
                'api_key': api_key,
                'auto_sync': auto_sync,
                'last_configured': datetime.now().isoformat()
            }
            
            for key, value in settings.items():
                self.db.cursor.execute('''
                    INSERT OR REPLACE INTO sync_settings (key, value, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                ''', (key, str(value)))
            
            self.db.conn.commit()
            return True
            
        except Exception as e:
            self.db.logger.error(f"Error configuring sync: {e}")
            return False
    
    def load_sync_settings(self):
        """تحميل إعدادات المزامنة"""
        try:
            self.db.cursor.execute("SELECT key, value FROM sync_settings")
            settings = dict(self.db.cursor.fetchall())
            
            self.api_base_url = settings.get('api_base_url', self.api_base_url)
            self.api_key = settings.get('api_key', self.api_key)
            self.sync_enabled = settings.get('auto_sync', 'False') == 'True'
            
        except Exception as e:
            self.db.logger.error(f"Error loading sync settings: {e}")
    
    def calculate_record_hash(self, table_name: str, record_data: Dict) -> str:
        """حساب hash للسجل"""
        # إزالة الحقول التي لا تؤثر على المحتوى
        excluded_fields = ['id', 'created_at', 'updated_at', 'last_sync']
        filtered_data = {k: v for k, v in record_data.items() if k not in excluded_fields}
        
        # ترتيب البيانات وتحويلها لـ JSON
        sorted_data = json.dumps(filtered_data, sort_keys=True, ensure_ascii=False)
        
        # حساب SHA-256 hash
        return hashlib.sha256(sorted_data.encode('utf-8')).hexdigest()
    
    def get_local_changes(self) -> List[Dict]:
        """الحصول على التغييرات المحلية"""
        changes = []
        
        try:
            # جداول للمزامنة
            sync_tables = ['clients', 'categories', 'orders', 'products']
            
            for table in sync_tables:
                # الحصول على السجلات المحدثة
                query = f'''
                    SELECT * FROM {table}
                    WHERE updated_at > (
                        SELECT COALESCE(MAX(last_sync), '1970-01-01') 
                        FROM sync_status 
                        WHERE table_name = ?
                    )
                '''
                
                self.db.cursor.execute(query, (table,))
                records = [dict(row) for row in self.db.cursor.fetchall()]
                
                for record in records:
                    record_hash = self.calculate_record_hash(table, record)
                    
                    changes.append({
                        'table': table,
                        'record_id': record['id'],
                        'data': record,
                        'hash': record_hash,
                        'operation': 'update'  # يمكن تحسينها لتمييز insert/update/delete
                    })
            
            return changes
            
        except Exception as e:
            self.db.logger.error(f"Error getting local changes: {e}")
            return []
    
    def upload_changes(self, changes: List[Dict]) -> bool:
        """رفع التغييرات للخادم"""
        if not self.api_key or not changes:
            return False
        
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'changes': changes,
                'timestamp': datetime.now().isoformat(),
                'device_id': self._get_device_id()
            }
            
            response = requests.post(
                f"{self.api_base_url}/sync/upload",
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # تحديث حالة المزامنة
                for change in changes:
                    self._update_sync_status(
                        change['table'],
                        change['record_id'],
                        change['hash'],
                        result.get('remote_hash', change['hash']),
                        'synced'
                    )
                
                self._log_sync_operation('upload', None, None, 'success', f"Uploaded {len(changes)} changes")
                return True
            else:
                self._log_sync_operation('upload', None, None, 'failed', f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.db.logger.error(f"Error uploading changes: {e}")
            self._log_sync_operation('upload', None, None, 'error', str(e))
            return False
    
    def download_changes(self) -> List[Dict]:
        """تحميل التغييرات من الخادم"""
        if not self.api_key:
            return []
        
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            # الحصول على آخر وقت مزامنة
            last_sync = self.last_sync or datetime(1970, 1, 1)
            
            params = {
                'since': last_sync.isoformat(),
                'device_id': self._get_device_id()
            }
            
            response = requests.get(
                f"{self.api_base_url}/sync/download",
                params=params,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                changes = result.get('changes', [])
                
                self._log_sync_operation('download', None, None, 'success', f"Downloaded {len(changes)} changes")
                return changes
            else:
                self._log_sync_operation('download', None, None, 'failed', f"HTTP {response.status_code}")
                return []
                
        except Exception as e:
            self.db.logger.error(f"Error downloading changes: {e}")
            self._log_sync_operation('download', None, None, 'error', str(e))
            return []
    
    def apply_remote_changes(self, changes: List[Dict]) -> bool:
        """تطبيق التغييرات البعيدة"""
        try:
            for change in changes:
                table = change['table']
                record_id = change['record_id']
                data = change['data']
                operation = change['operation']
                
                if operation == 'insert':
                    self._insert_record(table, data)
                elif operation == 'update':
                    self._update_record(table, record_id, data)
                elif operation == 'delete':
                    self._delete_record(table, record_id)
                
                # تحديث حالة المزامنة
                self._update_sync_status(
                    table, record_id, 
                    change.get('remote_hash', ''),
                    change.get('remote_hash', ''),
                    'synced'
                )
            
            self.db.conn.commit()
            return True
            
        except Exception as e:
            self.db.logger.error(f"Error applying remote changes: {e}")
            self.db.conn.rollback()
            return False
    
    def _insert_record(self, table: str, data: Dict):
        """إدراج سجل جديد"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        values = list(data.values())
        
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        self.db.cursor.execute(query, values)
    
    def _update_record(self, table: str, record_id: int, data: Dict):
        """تحديث سجل موجود"""
        set_clause = ', '.join([f"{k} = ?" for k in data.keys()])
        values = list(data.values()) + [record_id]
        
        query = f"UPDATE {table} SET {set_clause} WHERE id = ?"
        self.db.cursor.execute(query, values)
    
    def _delete_record(self, table: str, record_id: int):
        """حذف سجل"""
        query = f"DELETE FROM {table} WHERE id = ?"
        self.db.cursor.execute(query, (record_id,))
    
    def _update_sync_status(self, table: str, record_id: int, local_hash: str, 
                           remote_hash: str, status: str):
        """تحديث حالة المزامنة"""
        self.db.cursor.execute('''
            INSERT OR REPLACE INTO sync_status 
            (table_name, record_id, local_hash, remote_hash, last_sync, sync_status)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?)
        ''', (table, record_id, local_hash, remote_hash, status))
    
    def _log_sync_operation(self, operation: str, table: str, record_id: int, 
                           status: str, message: str):
        """تسجيل عملية المزامنة"""
        self.db.cursor.execute('''
            INSERT INTO sync_log (operation, table_name, record_id, status, message)
            VALUES (?, ?, ?, ?, ?)
        ''', (operation, table, record_id, status, message))
    
    def _get_device_id(self) -> str:
        """الحصول على معرف الجهاز"""
        try:
            import platform
            import uuid
            
            # إنشاء معرف فريد للجهاز
            device_info = f"{platform.node()}-{platform.system()}-{platform.machine()}"
            device_id = hashlib.md5(device_info.encode()).hexdigest()
            
            return device_id
            
        except:
            return "unknown-device"
    
    def sync_now(self) -> bool:
        """مزامنة فورية"""
        if self.is_syncing:
            return False
        
        self.is_syncing = True
        
        try:
            # رفع التغييرات المحلية
            local_changes = self.get_local_changes()
            if local_changes:
                upload_success = self.upload_changes(local_changes)
                if not upload_success:
                    return False
            
            # تحميل التغييرات البعيدة
            remote_changes = self.download_changes()
            if remote_changes:
                apply_success = self.apply_remote_changes(remote_changes)
                if not apply_success:
                    return False
            
            # تحديث وقت آخر مزامنة
            self.last_sync = datetime.now()
            self.db.set_setting('last_sync', self.last_sync.isoformat())
            
            return True
            
        except Exception as e:
            self.db.logger.error(f"Error in sync_now: {e}")
            return False
        finally:
            self.is_syncing = False
    
    def start_auto_sync(self):
        """بدء المزامنة التلقائية"""
        if not self.sync_enabled:
            return
        
        def auto_sync_worker():
            while self.sync_enabled:
                try:
                    self.sync_now()
                    threading.Event().wait(self.sync_interval)
                except Exception as e:
                    self.db.logger.error(f"Error in auto sync: {e}")
        
        thread = threading.Thread(target=auto_sync_worker, daemon=True)
        thread.start()
    
    def get_sync_status(self) -> Dict:
        """الحصول على حالة المزامنة"""
        try:
            # إحصائيات المزامنة
            self.db.cursor.execute('''
                SELECT sync_status, COUNT(*) as count
                FROM sync_status
                GROUP BY sync_status
            ''')
            status_counts = dict(self.db.cursor.fetchall())
            
            # آخر عمليات المزامنة
            self.db.cursor.execute('''
                SELECT operation, status, COUNT(*) as count
                FROM sync_log
                WHERE timestamp > datetime('now', '-1 day')
                GROUP BY operation, status
            ''')
            recent_operations = [dict(row) for row in self.db.cursor.fetchall()]
            
            return {
                'last_sync': self.last_sync.isoformat() if self.last_sync else None,
                'is_syncing': self.is_syncing,
                'sync_enabled': self.sync_enabled,
                'status_counts': status_counts,
                'recent_operations': recent_operations
            }
            
        except Exception as e:
            self.db.logger.error(f"Error getting sync status: {e}")
            return {}
    
    def resolve_conflict(self, table: str, record_id: int, resolution: str) -> bool:
        """حل تعارض المزامنة"""
        try:
            if resolution == 'local':
                # استخدام النسخة المحلية
                local_data = self._get_local_record(table, record_id)
                if local_data:
                    self.upload_changes([{
                        'table': table,
                        'record_id': record_id,
                        'data': local_data,
                        'hash': self.calculate_record_hash(table, local_data),
                        'operation': 'update'
                    }])
            
            elif resolution == 'remote':
                # تحميل النسخة البعيدة
                remote_changes = self.download_changes()
                relevant_changes = [
                    c for c in remote_changes 
                    if c['table'] == table and c['record_id'] == record_id
                ]
                if relevant_changes:
                    self.apply_remote_changes(relevant_changes)
            
            # تحديث حالة المزامنة
            self._update_sync_status(table, record_id, '', '', 'resolved')
            self.db.conn.commit()
            
            return True
            
        except Exception as e:
            self.db.logger.error(f"Error resolving conflict: {e}")
            return False
    
    def _get_local_record(self, table: str, record_id: int) -> Optional[Dict]:
        """الحصول على سجل محلي"""
        try:
            self.db.cursor.execute(f"SELECT * FROM {table} WHERE id = ?", (record_id,))
            result = self.db.cursor.fetchone()
            return dict(result) if result else None
        except Exception as e:
            self.db.logger.error(f"Error getting local record: {e}")
            return None
