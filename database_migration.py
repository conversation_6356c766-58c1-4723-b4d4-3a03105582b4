"""
Database Migration Script
يقوم بترقية قاعدة البيانات الموجودة إلى الإصدار الجديد
"""

import sqlite3
import logging
from datetime import datetime
from typing import List, Tuple


class DatabaseMigration:
    """مدير ترقية قاعدة البيانات"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.logger = logging.getLogger(__name__)
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            self.cursor = self.conn.cursor()
            self.logger.info(f"Connected to database: {self.db_path}")
        except sqlite3.Error as e:
            self.logger.error(f"Database connection error: {e}")
            raise
    
    def close(self):
        """إغلاق الاتصال"""
        if self.conn:
            self.conn.close()
            self.logger.info("Database connection closed")
    
    def get_current_version(self) -> int:
        """الحصول على إصدار قاعدة البيانات الحالي"""
        try:
            # التحقق من وجود جدول الإصدارات
            self.cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='schema_version'
            """)
            
            if not self.cursor.fetchone():
                # إنشاء جدول الإصدارات
                self.cursor.execute("""
                    CREATE TABLE schema_version (
                        version INTEGER PRIMARY KEY,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                self.cursor.execute("INSERT INTO schema_version (version) VALUES (0)")
                self.conn.commit()
                return 0
            
            # الحصول على أحدث إصدار
            self.cursor.execute("SELECT MAX(version) FROM schema_version")
            result = self.cursor.fetchone()
            return result[0] if result[0] is not None else 0
            
        except sqlite3.Error as e:
            self.logger.error(f"Error getting current version: {e}")
            return 0
    
    def set_version(self, version: int):
        """تعيين إصدار قاعدة البيانات"""
        try:
            self.cursor.execute(
                "INSERT INTO schema_version (version) VALUES (?)", 
                (version,)
            )
            self.conn.commit()
            self.logger.info(f"Database version set to {version}")
        except sqlite3.Error as e:
            self.logger.error(f"Error setting version: {e}")
            raise
    
    def column_exists(self, table_name: str, column_name: str) -> bool:
        """التحقق من وجود عمود في جدول"""
        try:
            self.cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row[1] for row in self.cursor.fetchall()]
            return column_name in columns
        except sqlite3.Error:
            return False
    
    def table_exists(self, table_name: str) -> bool:
        """التحقق من وجود جدول"""
        try:
            self.cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (table_name,))
            return self.cursor.fetchone() is not None
        except sqlite3.Error:
            return False
    
    def migration_v1_to_v2(self):
        """ترقية من الإصدار 1 إلى 2 - إضافة حقول جديدة للعملاء"""
        try:
            self.logger.info("Running migration v1 to v2...")
            
            # إضافة حقول جديدة لجدول العملاء
            new_columns = [
                ("company", "TEXT"),
                ("job_title", "TEXT"),
                ("notes", "TEXT"),
                ("client_type", "TEXT DEFAULT 'individual'"),
                ("status", "TEXT DEFAULT 'active'"),
                ("credit_limit", "DECIMAL(10,2) DEFAULT 0"),
                ("current_balance", "DECIMAL(10,2) DEFAULT 0"),
                ("last_order_date", "TIMESTAMP"),
                ("updated_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            ]
            
            for column_name, column_type in new_columns:
                if not self.column_exists("clients", column_name):
                    self.cursor.execute(f"ALTER TABLE clients ADD COLUMN {column_name} {column_type}")
                    self.logger.info(f"Added column {column_name} to clients table")
            
            self.conn.commit()
            self.set_version(2)
            
        except sqlite3.Error as e:
            self.logger.error(f"Migration v1 to v2 failed: {e}")
            self.conn.rollback()
            raise
    
    def migration_v2_to_v3(self):
        """ترقية من الإصدار 2 إلى 3 - تحسين جدول الفئات"""
        try:
            self.logger.info("Running migration v2 to v3...")
            
            # إضافة حقول جديدة لجدول الفئات
            new_columns = [
                ("parent_id", "INTEGER"),
                ("is_active", "BOOLEAN DEFAULT 1"),
                ("sort_order", "INTEGER DEFAULT 0"),
                ("image_path", "TEXT"),
                ("updated_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            ]
            
            for column_name, column_type in new_columns:
                if not self.column_exists("categories", column_name):
                    self.cursor.execute(f"ALTER TABLE categories ADD COLUMN {column_name} {column_type}")
                    self.logger.info(f"Added column {column_name} to categories table")
            
            # إضافة فهرس للفئة الأب
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_category_parent ON categories(parent_id)")
            
            self.conn.commit()
            self.set_version(3)
            
        except sqlite3.Error as e:
            self.logger.error(f"Migration v2 to v3 failed: {e}")
            self.conn.rollback()
            raise
    
    def migration_v3_to_v4(self):
        """ترقية من الإصدار 3 إلى 4 - تحسين جدول الطلبات"""
        try:
            self.logger.info("Running migration v3 to v4...")
            
            # إضافة حقول جديدة لجدول الطلبات
            new_columns = [
                ("order_number", "TEXT UNIQUE"),
                ("unit_price", "DECIMAL(10,2) DEFAULT 0"),
                ("total_price", "DECIMAL(10,2) DEFAULT 0"),
                ("discount", "DECIMAL(5,2) DEFAULT 0"),
                ("tax_rate", "DECIMAL(5,2) DEFAULT 0"),
                ("delivery_address", "TEXT"),
                ("delivery_date", "DATE"),
                ("status", "TEXT DEFAULT 'pending'"),
                ("priority", "TEXT DEFAULT 'normal'"),
                ("created_by", "INTEGER"),
                ("updated_at", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            ]
            
            for column_name, column_type in new_columns:
                if not self.column_exists("orders", column_name):
                    self.cursor.execute(f"ALTER TABLE orders ADD COLUMN {column_name} {column_type}")
                    self.logger.info(f"Added column {column_name} to orders table")
            
            # إنشاء أرقام طلبات للطلبات الموجودة
            self.cursor.execute("SELECT id FROM orders WHERE order_number IS NULL")
            orders = self.cursor.fetchall()
            
            for order in orders:
                order_number = f"ORD-{order['id']:06d}"
                self.cursor.execute(
                    "UPDATE orders SET order_number = ? WHERE id = ?",
                    (order_number, order['id'])
                )
            
            self.conn.commit()
            self.set_version(4)
            
        except sqlite3.Error as e:
            self.logger.error(f"Migration v3 to v4 failed: {e}")
            self.conn.rollback()
            raise
    
    def migration_v4_to_v5(self):
        """ترقية من الإصدار 4 إلى 5 - إضافة جداول جديدة"""
        try:
            self.logger.info("Running migration v4 to v5...")
            
            # إنشاء جدول المنتجات
            if not self.table_exists("products"):
                self.cursor.execute('''
                    CREATE TABLE products (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        description TEXT,
                        category_id INTEGER NOT NULL,
                        sku TEXT UNIQUE,
                        barcode TEXT,
                        price DECIMAL(10,2) DEFAULT 0,
                        cost DECIMAL(10,2) DEFAULT 0,
                        stock_quantity INTEGER DEFAULT 0,
                        min_stock_level INTEGER DEFAULT 0,
                        unit TEXT DEFAULT 'piece',
                        weight DECIMAL(8,3),
                        dimensions TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (category_id) REFERENCES categories (id)
                    )
                ''')
                self.logger.info("Created products table")
            
            # إنشاء جدول عناصر الطلبات
            if not self.table_exists("order_items"):
                self.cursor.execute('''
                    CREATE TABLE order_items (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        order_id INTEGER NOT NULL,
                        product_id INTEGER,
                        product_name TEXT NOT NULL,
                        quantity INTEGER NOT NULL,
                        unit_price DECIMAL(10,2) NOT NULL,
                        total_price DECIMAL(10,2) NOT NULL,
                        notes TEXT,
                        FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
                        FOREIGN KEY (product_id) REFERENCES products (id)
                    )
                ''')
                self.logger.info("Created order_items table")
            
            # إنشاء جدول سجل التدقيق
            if not self.table_exists("audit_log"):
                self.cursor.execute('''
                    CREATE TABLE audit_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT NOT NULL,
                        record_id INTEGER NOT NULL,
                        action TEXT NOT NULL,
                        old_values TEXT,
                        new_values TEXT,
                        user_id INTEGER,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users (id)
                    )
                ''')
                self.logger.info("Created audit_log table")
            
            # إنشاء جدول الإعدادات
            if not self.table_exists("settings"):
                self.cursor.execute('''
                    CREATE TABLE settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key TEXT UNIQUE NOT NULL,
                        value TEXT,
                        description TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                self.logger.info("Created settings table")
            
            self.conn.commit()
            self.set_version(5)
            
        except sqlite3.Error as e:
            self.logger.error(f"Migration v4 to v5 failed: {e}")
            self.conn.rollback()
            raise
    
    def run_migrations(self):
        """تشغيل جميع الترقيات المطلوبة"""
        try:
            self.connect()
            current_version = self.get_current_version()
            target_version = 5  # الإصدار المستهدف
            
            self.logger.info(f"Current database version: {current_version}")
            self.logger.info(f"Target database version: {target_version}")
            
            if current_version >= target_version:
                self.logger.info("Database is already up to date")
                return True
            
            # تشغيل الترقيات المطلوبة
            migrations = [
                (1, self.migration_v1_to_v2),
                (2, self.migration_v2_to_v3),
                (3, self.migration_v3_to_v4),
                (4, self.migration_v4_to_v5)
            ]
            
            for version, migration_func in migrations:
                if current_version < version + 1:
                    migration_func()
                    self.logger.info(f"Migration to version {version + 1} completed")
            
            self.logger.info("All migrations completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            return False
        finally:
            self.close()


def main():
    """تشغيل الترقيات"""
    logging.basicConfig(level=logging.INFO)
    
    migration = DatabaseMigration("sales_rep.db")
    success = migration.run_migrations()
    
    if success:
        print("Database migration completed successfully!")
    else:
        print("Database migration failed!")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
