import re

def validate_phone(phone):
    """
    Validate phone number format
    
    Args:
        phone: Phone number string
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if not phone:
        return False, "رقم الهاتف مطلوب"
    
    # Remove any non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Check if phone number has at least 10 digits
    if len(digits_only) < 10:
        return False, "يجب أن يحتوي رقم الهاتف على 10 أرقام على الأقل"
    
    return True, ""

def validate_email(email):
    """
    Validate email format
    
    Args:
        email: Email string
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if not email:
        return True, ""  # Email is optional
    
    # Simple email validation using regex
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        return False, "صيغة البريد الإلكتروني غير صحيحة"
    
    return True, ""

def validate_required(value, field_name):
    """
    Validate that a required field is not empty
    
    Args:
        value: Field value
        field_name: Name of the field for error message
    
    Returns:
        tuple: (is_valid, error_message)
    """
    field_names = {
        "Name": "الاسم",
        "Address": "العنوان",
        "Order type": "نوع الطلب",
        "Size": "الحجم",
        "Quantity": "الكمية",
        "Delivery method": "طريقة التوصيل"
    }
    
    arabic_field_name = field_names.get(field_name, field_name)
    
    if not value:
        return False, f"{arabic_field_name} مطلوب"
    
    return True, ""

def validate_numeric(value, field_name):
    """
    Validate that a field contains a numeric value
    
    Args:
        value: Field value
        field_name: Name of the field for error message
    
    Returns:
        tuple: (is_valid, error_message)
    """
    field_names = {
        "Quantity": "الكمية"
    }
    
    arabic_field_name = field_names.get(field_name, field_name)
    
    if not value:
        return False, f"{arabic_field_name} مطلوب"
    
    try:
        numeric_value = int(value)
        if numeric_value <= 0:
            return False, f"{arabic_field_name} يجب أن يكون أكبر من صفر"
    except ValueError:
        return False, f"{arabic_field_name} يجب أن يكون رقمًا"
    
    return True, ""

def validate_client_form(name, phone, address, email):
    """
    Validate client form inputs
    
    Args:
        name: Client name
        phone: Phone number
        address: Address
        email: Email (optional)
    
    Returns:
        tuple: (is_valid, error_message)
    """
    # Validate required fields
    name_valid, name_error = validate_required(name, "Name")
    if not name_valid:
        return name_valid, name_error
    
    # Validate phone
    phone_valid, phone_error = validate_phone(phone)
    if not phone_valid:
        return phone_valid, phone_error
    
    # Validate address
    address_valid, address_error = validate_required(address, "Address")
    if not address_valid:
        return address_valid, address_error
    
    # Validate email (optional)
    email_valid, email_error = validate_email(email)
    if not email_valid:
        return email_valid, email_error
    
    return True, ""

def validate_order_form(client_id, category_id, order_type, size, quantity, delivery_method):
    """
    Validate order form inputs
    
    Args:
        client_id: Client ID
        category_id: Category ID
        order_type: Order type
        size: Size
        quantity: Quantity
        delivery_method: Delivery method
    
    Returns:
        tuple: (is_valid, error_message)
    """
    # Validate client
    if not client_id:
        return False, "الرجاء اختيار عميل"
    
    # Validate category
    if not category_id:
        return False, "الرجاء اختيار فئة"
    
    # Validate order type
    order_type_valid, order_type_error = validate_required(order_type, "نوع الطلب")
    if not order_type_valid:
        return order_type_valid, order_type_error
    
    # Validate size
    size_valid, size_error = validate_required(size, "الحجم")
    if not size_valid:
        return size_valid, size_error
    
    # Validate quantity
    quantity_valid, quantity_error = validate_numeric(quantity, "الكمية")
    if not quantity_valid:
        return quantity_valid, quantity_error
    
    # Validate delivery method
    delivery_valid, delivery_error = validate_required(delivery_method, "طريقة التوصيل")
    if not delivery_valid:
        return delivery_valid, delivery_error
    
    return True, ""

def validate_category_form(name):
    """
    Validate category form inputs
    
    Args:
        name: Category name
    
    Returns:
        tuple: (is_valid, error_message)
    """
    # Validate name
    name_valid, name_error = validate_required(name, "اسم الفئة")
    if not name_valid:
        return name_valid, name_error
    
    return True, ""