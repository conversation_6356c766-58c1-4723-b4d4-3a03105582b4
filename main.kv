#:kivy 2.0.0
#:import NoTransition kivy.uix.screenmanager.NoTransition
#:import FadeTransition kivy.uix.screenmanager.FadeTransition
#:import SlideTransition kivy.uix.screenmanager.SlideTransition
#:import CardTransition kivy.uix.screenmanager.CardTransition
#:include screens/statistics_screen.kv

MDScreenManager:
    id: screen_manager
    transition: FadeTransition()
    
    SplashScreen:
        name: 'splash'
        id: splash_screen
    
    LoginScreen:
        name: 'login'
        id: login_screen
    
    MainScreen:
        name: 'main'
        id: main_screen
    
    AddClientScreen:
        name: 'add_client'
        id: add_client_screen
    
    AddOrderScreen:
        name: 'add_order'
        id: add_order_screen
    
    AddCategoryScreen:
        name: 'add_category'
        id: add_category_screen
    
    StatisticsScreen:
        name: 'statistics'
        id: statistics_screen
    
    ProfileScreen:
        name: 'profile'
        id: profile_screen

<SplashScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(20)
        spacing: dp(20)
        md_bg_color: app.theme_cls.primary_color
        
        Widget:
            size_hint_y: 0.3
        
        MDLabel:
            text: "أداة مندوب المبيعات"
            halign: "center"
            font_style: "H3"
            theme_text_color: "Custom"
            text_color: 1, 1, 1, 1
            size_hint_y: 0.2
        
        MDLabel:
            text: "مساعدك المتنقل للمبيعات"
            halign: "center"
            font_style: "H6"
            theme_text_color: "Custom"
            text_color: 1, 1, 1, 0.8
            size_hint_y: 0.1
        
        Widget:
            size_hint_y: 0.4

<LoginScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(20)
        spacing: dp(20)
        md_bg_color: app.theme_cls.bg_normal
        
        Widget:
            size_hint_y: 0.1
        
        MDCard:
            orientation: "vertical"
            size_hint: 0.9, None
            height: self.minimum_height
            pos_hint: {"center_x": 0.5}
            padding: dp(20)
            spacing: dp(20)
            elevation: 4
            radius: [10, 10, 10, 10]
            md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
            
            MDLabel:
                text: "تسجيل الدخول"
                halign: "center"
                font_style: "H4"
                size_hint_y: None
                height: self.texture_size[1]
                padding: [0, dp(10)]
            
            MDSeparator:
                height: dp(1)
            
            MDTextField:
                id: username
                hint_text: "اسم المستخدم"
                icon_right: "account"
                size_hint_x: 1
                pos_hint: {"center_x": 0.5}
                helper_text: "أدخل اسم المستخدم الخاص بك"
                helper_text_mode: "on_focus"
            
            MDTextField:
                id: password
                hint_text: "كلمة المرور"
                icon_right: "key-variant"
                password: True
                size_hint_x: 1
                pos_hint: {"center_x": 0.5}
                helper_text: "أدخل كلمة المرور الخاصة بك"
                helper_text_mode: "on_focus"
            
            MDRaisedButton:
                text: "تسجيل الدخول"
                pos_hint: {"center_x": 0.5}
                size_hint_x: 0.8
                on_release: root.verify_credentials()
            pos_hint: {"center_x": 0.5}
        
        MDRaisedButton:
            text: "Login"
            pos_hint: {"center_x": 0.5}
            on_release: root.verify_credentials()
        
        Widget:
            size_hint_y: 0.3

<MainScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        
        MDTopAppBar:
            title: "أداة مندوب المبيعات"
            elevation: 4
            right_action_items: [['theme-light-dark', lambda x: app.toggle_theme_style()], ['export', lambda x: app.show_export_options(x)]]
        
        MDBottomNavigation:
            panel_color: app.theme_cls.primary_color
            text_color_active: 1, 1, 1, 1
            
            MDBottomNavigationItem:
                name: "clients"
                text: "العملاء"
                icon: "account-group"
                
                ClientsScreen:
                    id: clients_screen
            
            MDBottomNavigationItem:
                name: "orders"
                text: "الطلبات"
                icon: "package-variant-closed"
                
                OrdersScreen:
                    id: orders_screen
            
            MDBottomNavigationItem:
                name: "categories"
                text: "الفئات"
                icon: "tag-multiple"
                
                CategoriesScreen:
                    id: categories_screen
            
            MDBottomNavigationItem:
                name: "statistics"
                text: "الإحصائيات"
                icon: "chart-bar"
                on_tab_release: 
                    app.root.ids.screen_manager.current = 'statistics'
            
            MDBottomNavigationItem:
                name: "profile"
                text: "الملف الشخصي"
                icon: "account-circle"
                on_tab_release: 
                    app.root.ids.screen_manager.current = 'profile'
                
                ProfileScreen:
                    id: profile_screen

<ClientsScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(10)
        
        MDBoxLayout:
            adaptive_height: True
            padding: [0, 0, 0, 10]
            
            MDTextField:
                id: search_client
                hint_text: "بحث عن العملاء"
                icon_right: "magnify"
                size_hint_x: 0.8
            
            MDIconButton:
                icon: "plus"
                pos_hint: {"center_y": .5}
                tooltip_text: "إضافة عميل جديد"
                on_release: root.show_add_client_dialog()
        
        ScrollView:
            MDList:
                id: clients_list

<AddClientScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(20)
        spacing: dp(10)
        md_bg_color: app.theme_cls.bg_normal
        
        MDTopAppBar:
            id: top_app_bar
            title: "إضافة عميل جديد"
            elevation: 10
            left_action_items: [["arrow-left", lambda x: root.cancel()]]
        
        MDCard:
            orientation: "vertical"
            size_hint: 0.95, None
            height: self.minimum_height
            pos_hint: {"center_x": 0.5}
            padding: dp(20)
            spacing: dp(10)
            elevation: 4
            radius: [10, 10, 10, 10]
            md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
            
            MDLabel:
                text: "معلومات العميل"
                halign: "center"
                font_style: "H6"
                size_hint_y: None
                height: self.texture_size[1]
                padding: [0, dp(10)]
            
            MDSeparator:
                height: dp(1)
            
            MDTextField:
                id: name
                hint_text: "الاسم الكامل*"
                helper_text: "مطلوب"
                helper_text_mode: "on_focus"
                icon_right: "account"
            
            MDTextField:
                id: phone
                hint_text: "رقم الهاتف*"
                helper_text: "مطلوب"
                helper_text_mode: "on_focus"
                input_filter: "int"
                icon_right: "phone"
            
            MDTextField:
                id: address
                hint_text: "العنوان*"
                helper_text: "مطلوب"
                helper_text_mode: "on_focus"
                icon_right: "map-marker"
            
            MDTextField:
                id: email
                hint_text: "البريد الإلكتروني"
                helper_text: "اختياري"
                helper_text_mode: "on_focus"
                icon_right: "email"
        
        Widget:
            size_hint_y: 0.1
        
        MDRaisedButton:
            id: save_button
            text: "حفظ"
            pos_hint: {"center_x": 0.5}
            size_hint_x: 0.8
            on_release: root.save_client()
            helper_text_mode: "on_focus"
        
        Widget:
            size_hint_y: 0.1
        
        MDBoxLayout:
            adaptive_height: True
            spacing: dp(10)
            
            MDRaisedButton:
                text: "حفظ"
                on_release: root.save_client()
                size_hint_x: 0.5
            
            MDFlatButton:
                text: "إلغاء"
                on_release: root.cancel()
                size_hint_x: 0.5

<OrdersScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(10)
        
        MDBoxLayout:
            adaptive_height: True
            padding: [0, 0, 0, 10]
            
            MDTextField:
                id: search_order
                hint_text: "بحث عن الطلبات"
                icon_right: "magnify"
                size_hint_x: 0.8
            
            MDIconButton:
                icon: "plus"
                pos_hint: {"center_y": .5}
                tooltip_text: "إضافة طلب جديد"
                on_release: root.show_add_order_dialog()
        
        ScrollView:
            MDList:
                id: orders_list

<AddOrderScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(20)
        spacing: dp(10)
        md_bg_color: app.theme_cls.bg_normal
        
        MDTopAppBar:
            id: top_app_bar
            title: "إضافة طلب جديد"
            elevation: 10
            left_action_items: [["arrow-left", lambda x: root.cancel()]]
        
        ScrollView:
            MDBoxLayout:
                orientation: 'vertical'
                spacing: dp(10)
                adaptive_height: True
                padding: [0, 0, 0, 20]
                
                MDCard:
                    orientation: "vertical"
                    size_hint: 0.95, None
                    height: self.minimum_height
                    pos_hint: {"center_x": 0.5}
                    padding: dp(20)
                    spacing: dp(10)
                    elevation: 4
                    radius: [10, 10, 10, 10]
                    md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
                    
                    MDLabel:
                        text: "معلومات الطلب"
                        halign: "center"
                        font_style: "H6"
                        size_hint_y: None
                        height: self.texture_size[1]
                        padding: [0, dp(10)]
                    
                    MDSeparator:
                        height: dp(1)
                    
                    MDDropDownItem:
                        id: client_id
                        text: "اختر العميل"
                        pos_hint: {"center_x": .5}
                    
                    MDDropDownItem:
                        id: category_id
                        text: "اختر الفئة"
                        pos_hint: {"center_x": .5}
                    
                    MDTextField:
                        id: order_type
                        hint_text: "نوع الطلب*"
                        helper_text: "مطلوب"
                        helper_text_mode: "on_focus"
                        icon_right: "package-variant"
                    
                    MDTextField:
                        id: size
                        hint_text: "الحجم*"
                        helper_text: "مطلوب"
                        helper_text_mode: "on_focus"
                        icon_right: "ruler"
                    
                    MDTextField:
                        id: quantity
                        hint_text: "الكمية*"
                        helper_text: "مطلوب"
                        helper_text_mode: "on_focus"
                        input_filter: "int"
                        icon_right: "numeric"
                    
                    MDTextField:
                        id: delivery_method
                        hint_text: "طريقة التوصيل*"
                        helper_text: "مطلوب"
                        helper_text_mode: "on_focus"
                        icon_right: "truck-delivery"
                    
                    MDTextField:
                        id: notes
                        hint_text: "ملاحظات"
                        helper_text: "اختياري"
                        helper_text_mode: "on_focus"
                        multiline: True
                        icon_right: "note-text"
        
        MDBoxLayout:
            adaptive_height: True
            spacing: dp(10)
            padding: [dp(20), dp(10), dp(20), dp(20)]
            
            MDRaisedButton:
                id: save_button
                text: "حفظ"
                on_release: root.save_order()
                size_hint_x: 0.5
                md_bg_color: app.theme_cls.primary_color
            
            MDFlatButton:
                text: "إلغاء"
                on_release: root.cancel()
                size_hint_x: 0.5
                text_color: app.theme_cls.primary_color

<CategoriesScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(10)
        
        MDBoxLayout:
            adaptive_height: True
            padding: [0, 0, 0, 10]
            
            MDTextField:
                id: search_category
                hint_text: "بحث عن الفئات"
                icon_right: "magnify"
                size_hint_x: 0.8
            
            MDIconButton:
                icon: "plus"
                pos_hint: {"center_y": .5}
                on_release: root.show_add_category_dialog()
                tooltip_text: "إضافة فئة جديدة"
        
        ScrollView:
            MDList:
                id: categories_list

<AddCategoryScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(20)
        spacing: dp(10)
        
        MDTopAppBar:
            id: top_app_bar
            title: "إضافة فئة جديدة"
            elevation: 10
            left_action_items: [["arrow-left", lambda x: root.cancel()]]
        
        MDTextField:
            id: name
            hint_text: "اسم الفئة*"
            helper_text: "مطلوب"
            helper_text_mode: "on_focus"
            icon_right: "tag"
        
        MDTextField:
            id: description
            hint_text: "الوصف"
            helper_text: "اختياري"
            helper_text_mode: "on_focus"
            multiline: True
            icon_right: "text-box"
        
        Widget:
            size_hint_y: 0.1
        
        MDBoxLayout:
            adaptive_height: True
            spacing: dp(10)
            
            MDRaisedButton:
                id: save_button
                text: "حفظ"
                on_release: root.save_category()
                size_hint_x: 0.5
                md_bg_color: app.theme_cls.primary_color
            
            MDFlatButton:
                text: "إلغاء"
                on_release: root.cancel()
                size_hint_x: 0.5
                text_color: app.theme_cls.primary_color

<ProfileScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        padding: dp(20)
        
        MDTopAppBar:
            title: "الملف الشخصي"
            elevation: 4
            pos_hint: {"top": 1}
            left_action_items: [['arrow-left', lambda x: app.root.ids.screen_manager.current = 'main']]
        
        MDCard:
            orientation: "vertical"
            padding: dp(20)
            size_hint: 0.9, None
            height: self.minimum_height
            pos_hint: {"center_x": 0.5}
            md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
            radius: [10, 10, 10, 10]
            elevation: 4
            spacing: dp(10)
            
            MDBoxLayout:
                orientation: "horizontal"
                adaptive_height: True
                padding: [0, dp(10), 0, dp(10)]
                
                MDIconButton:
                    icon: "account-circle"
                    icon_size: "64sp"
                    pos_hint: {"center_y": 0.5}
                
                MDBoxLayout:
                    orientation: "vertical"
                    adaptive_height: True
                    spacing: dp(5)
                    padding: [dp(10), 0, 0, 0]
                    
                    MDLabel:
                        text: "مندوب المبيعات"
                        font_style: "H6"
                        bold: True
                    
                    MDLabel:
                        text: "<EMAIL>"
                        font_style: "Body1"
                    
                    MDLabel:
                        text: "رقم التعريف: SR-12345"
                        font_style: "Body2"
        
        MDCard:
            orientation: "vertical"
            padding: dp(20)
            size_hint: 0.9, None
            height: self.minimum_height
            pos_hint: {"center_x": 0.5}
            md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
            radius: [10, 10, 10, 10]
            elevation: 4
            spacing: dp(15)
            margin: [0, dp(20), 0, 0]
            
            MDLabel:
                text: "الإعدادات"
                font_style: "H6"
                bold: True
                adaptive_height: True
            
            MDBoxLayout:
                orientation: "horizontal"
                adaptive_height: True
                
                MDIcon:
                    icon: "theme-light-dark"
                    pos_hint: {"center_y": 0.5}
                
                MDLabel:
                    text: "الوضع الداكن"
                    pos_hint: {"center_y": 0.5}
                
                MDSwitch:
                    active: app.theme_cls.theme_style == "Dark"
                    pos_hint: {"center_y": 0.5}
                    on_active: app.toggle_theme_style()
        
        Widget:
            size_hint_y: 0.1
        
        MDCard:
            orientation: "vertical"
            padding: dp(10)
            size_hint: 0.9, None
            height: logout_button.height + dp(20)
            pos_hint: {"center_x": 0.5}
            md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
            radius: [10, 10, 10, 10]
            elevation: 4
            
            MDRaisedButton:
                id: logout_button
                text: "تسجيل الخروج"
                pos_hint: {"center_x": 0.5}
                on_release: root.logout()
                md_bg_color: app.theme_cls.error_color