import sqlite3
import os
from datetime import datetime
from functools import lru_cache
import threading

class DatabaseManager:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_name="sales_rep.db"):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DatabaseManager, cls).__new__(cls)
                cls._instance.db_name = db_name
                cls._instance.conn = None
                cls._instance.cursor = None
                cls._instance.connect()
                cls._instance.create_tables()
                cls._instance.create_indexes()
                # Initialize cache
                cls._instance._clear_cache()
            return cls._instance
    
    def __init__(self, db_name="sales_rep.db"):
        # __new__ handles initialization
        pass
    
    def _clear_cache(self):
        """Clear all method caches"""
        self.get_all_clients.cache_clear()
        self.get_all_categories.cache_clear()
        self.get_all_orders.cache_clear()
    
    def connect(self):
        """Connect to the SQLite database"""
        try:
            # Enable foreign keys
            self.conn = sqlite3.connect(self.db_name, check_same_thread=False)
            self.conn.execute("PRAGMA foreign_keys = ON")
            self.conn.row_factory = sqlite3.Row  # Enable row factory for named columns
            self.cursor = self.conn.cursor()
            print(f"Connected to database: {self.db_name}")
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
    
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            print("Database connection closed")
    
    def create_tables(self):
        """Create necessary tables if they don't exist"""
        try:
            # Clients table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT NOT NULL,
                    address TEXT NOT NULL,
                    email TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Categories table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Orders table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_id INTEGER NOT NULL,
                    category_id INTEGER NOT NULL,
                    order_type TEXT NOT NULL,
                    size TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    delivery_method TEXT NOT NULL,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (client_id) REFERENCES clients (id),
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')
            
            self.conn.commit()
            print("Tables created successfully")
        except sqlite3.Error as e:
            print(f"Error creating tables: {e}")
    
    def create_indexes(self):
        """Create indexes for better query performance"""
        try:
            # Index on client name for faster searches
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_client_name ON clients(name)')
            
            # Index on client phone for faster searches
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_client_phone ON clients(phone)')
            
            # Index on category name for faster searches
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_category_name ON categories(name)')
            
            # Indexes for orders table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_client ON orders(client_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_category ON orders(category_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_type ON orders(order_type)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_date ON orders(created_at)')
            
            self.conn.commit()
            print("Indexes created successfully")
        except sqlite3.Error as e:
            print(f"Error creating indexes: {e}")
    
    # Client operations
    def add_client(self, name, phone, address, email=""):
        """Add a new client to the database"""
        try:
            self.cursor.execute(
                "INSERT INTO clients (name, phone, address, email) VALUES (?, ?, ?, ?)",
                (name, phone, address, email)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_clients.cache_clear()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding client: {e}")
            return None
    
    @lru_cache(maxsize=1)
    def get_all_clients(self):
        """Get all clients from the database with caching"""
        try:
            self.cursor.execute("SELECT * FROM clients ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error fetching clients: {e}")
            return []
    
    def get_client_by_id(self, client_id):
        """Get a client by ID"""
        try:
            self.cursor.execute("SELECT * FROM clients WHERE id = ?", (client_id,))
            result = self.cursor.fetchone()
            return dict(result) if result else None
        except sqlite3.Error as e:
            print(f"Error fetching client by ID: {e}")
            return None
    
    def update_client(self, client_id, name, phone, address, email=""):
        """Update an existing client in the database"""
        try:
            self.cursor.execute(
                "UPDATE clients SET name = ?, phone = ?, address = ?, email = ? WHERE id = ?",
                (name, phone, address, email, client_id)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_clients.cache_clear()
            return True
        except sqlite3.Error as e:
            print(f"Error updating client: {e}")
            return False
    
    def search_clients(self, search_text):
        """Search clients by name, phone, or address"""
        try:
            search_pattern = f"%{search_text}%"
            self.cursor.execute("""
                SELECT * FROM clients 
                WHERE name LIKE ? OR phone LIKE ? OR address LIKE ?
                ORDER BY name
            """, (search_pattern, search_pattern, search_pattern))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching clients: {e}")
            return []
    
    # Category operations
    def add_category(self, name, description=""):
        """Add a new category to the database"""
        try:
            self.cursor.execute(
                "INSERT INTO categories (name, description) VALUES (?, ?)",
                (name, description)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_categories.cache_clear()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding category: {e}")
            return None
    
    @lru_cache(maxsize=1)
    def get_all_categories(self):
        """Get all categories from the database with caching"""
        try:
            self.cursor.execute("SELECT * FROM categories ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error fetching categories: {e}")
            return []
    
    def search_categories(self, search_text):
        """Search categories by name or description"""
        try:
            search_pattern = f"%{search_text}%"
            self.cursor.execute("""
                SELECT * FROM categories 
                WHERE name LIKE ? OR description LIKE ?
                ORDER BY name
            """, (search_pattern, search_pattern))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching categories: {e}")
            return []
    
    def update_category(self, category_id, name, description):
        """Update a category"""
        try:
            self.cursor.execute(
                "UPDATE categories SET name = ?, description = ? WHERE id = ?",
                (name, description, category_id)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_categories.cache_clear()
            return True
        except sqlite3.Error as e:
            print(f"Error updating category: {e}")
            return False
    
    def delete_category(self, category_id):
        """Delete a category"""
        try:
            # Check if category is used in any orders
            self.cursor.execute("SELECT COUNT(*) FROM orders WHERE category_id = ?", (category_id,))
            count = self.cursor.fetchone()[0]
            if count > 0:
                return False, f"Cannot delete: Category is used in {count} orders"
                
            self.cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_categories.cache_clear()
            return True, "Category deleted successfully"
        except sqlite3.Error as e:
            print(f"Error deleting category: {e}")
            return False, f"Error: {str(e)}"
    
    # Order operations
    def add_order(self, client_id, category_id, order_type, size, quantity, delivery_method, notes=""):
        """Add a new order to the database"""
        try:
            self.cursor.execute(
                """INSERT INTO orders 
                (client_id, category_id, order_type, size, quantity, delivery_method, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (client_id, category_id, order_type, size, quantity, delivery_method, notes)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_orders.cache_clear()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding order: {e}")
            return None
    
    @lru_cache(maxsize=1)
    def get_all_orders(self):
        """Get all orders with client and category information with caching"""
        try:
            self.cursor.execute("""
                SELECT o.*, c.name as client_name, cat.name as category_name
                FROM orders o
                JOIN clients c ON o.client_id = c.id
                JOIN categories cat ON o.category_id = cat.id
                ORDER BY o.created_at DESC
            """)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error fetching orders: {e}")
            return []
    
    def search_orders(self, search_text):
        """Search orders by client name, category name, or order type"""
        try:
            search_pattern = f"%{search_text}%"
            self.cursor.execute("""
                SELECT o.*, c.name as client_name, cat.name as category_name
                FROM orders o
                JOIN clients c ON o.client_id = c.id
                JOIN categories cat ON o.category_id = cat.id
                WHERE c.name LIKE ? OR cat.name LIKE ? OR o.order_type LIKE ?
                ORDER BY o.created_at DESC
            """, (search_pattern, search_pattern, search_pattern))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching orders: {e}")
            return []
    
    def get_client_orders(self, client_id):
        """Get all orders for a specific client"""
        try:
            self.cursor.execute("""
                SELECT o.*, c.name as client_name, cat.name as category_name
                FROM orders o
                JOIN clients c ON o.client_id = c.id
                JOIN categories cat ON o.category_id = cat.id
                WHERE o.client_id = ?
                ORDER BY o.created_at DESC
            """, (client_id,))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error fetching client orders: {e}")
            return []