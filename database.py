import sqlite3
import os
from datetime import datetime
from functools import lru_cache
import threading
import logging
from typing import Optional, List, Dict, Any

class DatabaseManager:
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, db_name="sales_rep.db"):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DatabaseManager, cls).__new__(cls)
                cls._instance.db_name = db_name
                cls._instance.conn = None
                cls._instance.cursor = None
                cls._instance._setup_logging()
                cls._instance.connect()
                cls._instance.create_tables()
                cls._instance.create_indexes()
                # Initialize cache
                cls._instance._clear_cache()
            return cls._instance
    
    def __init__(self, db_name="sales_rep.db"):
        # __new__ handles initialization
        pass

    def _setup_logging(self):
        """إعداد نظام التسجيل"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('sales_rep.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def _clear_cache(self):
        """Clear all method caches"""
        self.get_all_clients.cache_clear()
        self.get_all_categories.cache_clear()
        self.get_all_orders.cache_clear()
    
    def connect(self):
        """Connect to the SQLite database"""
        try:
            # Enable foreign keys and other optimizations
            self.conn = sqlite3.connect(
                self.db_name,
                check_same_thread=False,
                timeout=30.0  # 30 second timeout
            )

            # Enable foreign keys
            self.conn.execute("PRAGMA foreign_keys = ON")

            # Performance optimizations
            self.conn.execute("PRAGMA journal_mode = WAL")  # Write-Ahead Logging
            self.conn.execute("PRAGMA synchronous = NORMAL")  # Faster writes
            self.conn.execute("PRAGMA cache_size = 10000")  # Larger cache
            self.conn.execute("PRAGMA temp_store = MEMORY")  # Use memory for temp tables

            self.conn.row_factory = sqlite3.Row  # Enable row factory for named columns
            self.cursor = self.conn.cursor()

            self.logger.info(f"Connected to database: {self.db_name}")
        except sqlite3.Error as e:
            self.logger.error(f"Database connection error: {e}")
            raise
    
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            self.logger.info("Database connection closed")

    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_sales_rep_{timestamp}.db"

            # إنشاء اتصال جديد للنسخ الاحتياطي
            backup_conn = sqlite3.connect(backup_path)

            # نسخ قاعدة البيانات
            self.conn.backup(backup_conn)
            backup_conn.close()

            self.logger.info(f"Database backup created: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
            return False

    def restore_database(self, backup_path: str) -> bool:
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                self.logger.error(f"Backup file not found: {backup_path}")
                return False

            # إغلاق الاتصال الحالي
            self.close()

            # استبدال قاعدة البيانات الحالية
            import shutil
            shutil.copy2(backup_path, self.db_name)

            # إعادة الاتصال
            self.connect()

            self.logger.info(f"Database restored from: {backup_path}")
            return True

        except Exception as e:
            self.logger.error(f"Restore failed: {e}")
            return False
    
    def create_tables(self):
        """Create necessary tables if they don't exist"""
        try:
            # Clients table - Enhanced with more fields
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT NOT NULL,
                    address TEXT NOT NULL,
                    email TEXT,
                    company TEXT,
                    job_title TEXT,
                    notes TEXT,
                    client_type TEXT DEFAULT 'individual',
                    status TEXT DEFAULT 'active',
                    credit_limit DECIMAL(10,2) DEFAULT 0,
                    current_balance DECIMAL(10,2) DEFAULT 0,
                    last_order_date TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Categories table - Enhanced with more fields
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    parent_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    sort_order INTEGER DEFAULT 0,
                    image_path TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES categories (id)
                )
            ''')
            
            # Orders table - Enhanced with more fields
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_number TEXT UNIQUE,
                    client_id INTEGER NOT NULL,
                    category_id INTEGER NOT NULL,
                    order_type TEXT NOT NULL,
                    size TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_price DECIMAL(10,2) DEFAULT 0,
                    total_price DECIMAL(10,2) DEFAULT 0,
                    discount DECIMAL(5,2) DEFAULT 0,
                    tax_rate DECIMAL(5,2) DEFAULT 0,
                    delivery_method TEXT NOT NULL,
                    delivery_address TEXT,
                    delivery_date DATE,
                    status TEXT DEFAULT 'pending',
                    priority TEXT DEFAULT 'normal',
                    notes TEXT,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (client_id) REFERENCES clients (id),
                    FOREIGN KEY (category_id) REFERENCES categories (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            # Products table - for detailed product management
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    category_id INTEGER NOT NULL,
                    sku TEXT UNIQUE,
                    barcode TEXT,
                    price DECIMAL(10,2) DEFAULT 0,
                    cost DECIMAL(10,2) DEFAULT 0,
                    stock_quantity INTEGER DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 0,
                    unit TEXT DEFAULT 'piece',
                    weight DECIMAL(8,3),
                    dimensions TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')

            # Order items table - for detailed order line items
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS order_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    order_id INTEGER NOT NULL,
                    product_id INTEGER,
                    product_name TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_price DECIMAL(10,2) NOT NULL,
                    total_price DECIMAL(10,2) NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (order_id) REFERENCES orders (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # Audit log table - for tracking changes
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_name TEXT NOT NULL,
                    record_id INTEGER NOT NULL,
                    action TEXT NOT NULL,
                    old_values TEXT,
                    new_values TEXT,
                    user_id INTEGER,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # Settings table - for application settings
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            self.conn.commit()
            self.logger.info("Tables created successfully")
        except sqlite3.Error as e:
            self.logger.error(f"Error creating tables: {e}")
            raise
    
    def create_indexes(self):
        """Create indexes for better query performance"""
        try:
            # Indexes for clients table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_client_name ON clients(name)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_client_phone ON clients(phone)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_client_email ON clients(email)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_client_company ON clients(company)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_client_status ON clients(status)')

            # Indexes for categories table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_category_name ON categories(name)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_category_parent ON categories(parent_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_category_active ON categories(is_active)')

            # Indexes for orders table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_number ON orders(order_number)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_client ON orders(client_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_category ON orders(category_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_type ON orders(order_type)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_status ON orders(status)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_date ON orders(created_at)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_delivery_date ON orders(delivery_date)')

            # Indexes for products table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_name ON products(name)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_sku ON products(sku)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_barcode ON products(barcode)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_category ON products(category_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_product_active ON products(is_active)')

            # Indexes for order_items table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_item_order ON order_items(order_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_order_item_product ON order_items(product_id)')

            # Indexes for audit_log table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_table ON audit_log(table_name)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_record ON audit_log(record_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_user ON audit_log(user_id)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)')

            # Indexes for users table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_username ON users(username)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_email ON users(email)')
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_role ON users(role)')

            # Indexes for settings table
            self.cursor.execute('CREATE INDEX IF NOT EXISTS idx_setting_key ON settings(key)')

            self.conn.commit()
            self.logger.info("Indexes created successfully")
        except sqlite3.Error as e:
            self.logger.error(f"Error creating indexes: {e}")
            raise
    
    # Client operations
    def add_client(self, name, phone, address, email=""):
        """Add a new client to the database"""
        try:
            self.cursor.execute(
                "INSERT INTO clients (name, phone, address, email) VALUES (?, ?, ?, ?)",
                (name, phone, address, email)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_clients.cache_clear()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding client: {e}")
            return None
    
    @lru_cache(maxsize=1)
    def get_all_clients(self):
        """Get all clients from the database with caching"""
        try:
            self.cursor.execute("SELECT * FROM clients ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error fetching clients: {e}")
            return []
    
    def get_client_by_id(self, client_id):
        """Get a client by ID"""
        try:
            self.cursor.execute("SELECT * FROM clients WHERE id = ?", (client_id,))
            result = self.cursor.fetchone()
            return dict(result) if result else None
        except sqlite3.Error as e:
            print(f"Error fetching client by ID: {e}")
            return None
    
    def update_client(self, client_id, name, phone, address, email=""):
        """Update an existing client in the database"""
        try:
            self.cursor.execute(
                "UPDATE clients SET name = ?, phone = ?, address = ?, email = ? WHERE id = ?",
                (name, phone, address, email, client_id)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_clients.cache_clear()
            return True
        except sqlite3.Error as e:
            print(f"Error updating client: {e}")
            return False
    
    def search_clients(self, search_text):
        """Search clients by name, phone, or address"""
        try:
            search_pattern = f"%{search_text}%"
            self.cursor.execute("""
                SELECT * FROM clients 
                WHERE name LIKE ? OR phone LIKE ? OR address LIKE ?
                ORDER BY name
            """, (search_pattern, search_pattern, search_pattern))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching clients: {e}")
            return []
    
    # Category operations
    def add_category(self, name, description=""):
        """Add a new category to the database"""
        try:
            self.cursor.execute(
                "INSERT INTO categories (name, description) VALUES (?, ?)",
                (name, description)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_categories.cache_clear()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding category: {e}")
            return None
    
    @lru_cache(maxsize=1)
    def get_all_categories(self):
        """Get all categories from the database with caching"""
        try:
            self.cursor.execute("SELECT * FROM categories ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error fetching categories: {e}")
            return []
    
    def search_categories(self, search_text):
        """Search categories by name or description"""
        try:
            search_pattern = f"%{search_text}%"
            self.cursor.execute("""
                SELECT * FROM categories 
                WHERE name LIKE ? OR description LIKE ?
                ORDER BY name
            """, (search_pattern, search_pattern))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching categories: {e}")
            return []
    
    def update_category(self, category_id, name, description):
        """Update a category"""
        try:
            self.cursor.execute(
                "UPDATE categories SET name = ?, description = ? WHERE id = ?",
                (name, description, category_id)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_categories.cache_clear()
            return True
        except sqlite3.Error as e:
            print(f"Error updating category: {e}")
            return False
    
    def delete_category(self, category_id):
        """Delete a category"""
        try:
            # Check if category is used in any orders
            self.cursor.execute("SELECT COUNT(*) FROM orders WHERE category_id = ?", (category_id,))
            count = self.cursor.fetchone()[0]
            if count > 0:
                return False, f"Cannot delete: Category is used in {count} orders"
                
            self.cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_categories.cache_clear()
            return True, "Category deleted successfully"
        except sqlite3.Error as e:
            print(f"Error deleting category: {e}")
            return False, f"Error: {str(e)}"
    
    # Order operations
    def add_order(self, client_id, category_id, order_type, size, quantity, delivery_method, notes=""):
        """Add a new order to the database"""
        try:
            self.cursor.execute(
                """INSERT INTO orders 
                (client_id, category_id, order_type, size, quantity, delivery_method, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (client_id, category_id, order_type, size, quantity, delivery_method, notes)
            )
            self.conn.commit()
            # Clear cache when data changes
            self.get_all_orders.cache_clear()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding order: {e}")
            return None
    
    @lru_cache(maxsize=1)
    def get_all_orders(self):
        """Get all orders with client and category information with caching"""
        try:
            self.cursor.execute("""
                SELECT o.*, c.name as client_name, cat.name as category_name
                FROM orders o
                JOIN clients c ON o.client_id = c.id
                JOIN categories cat ON o.category_id = cat.id
                ORDER BY o.created_at DESC
            """)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error fetching orders: {e}")
            return []
    
    def search_orders(self, search_text):
        """Search orders by client name, category name, or order type"""
        try:
            search_pattern = f"%{search_text}%"
            self.cursor.execute("""
                SELECT o.*, c.name as client_name, cat.name as category_name
                FROM orders o
                JOIN clients c ON o.client_id = c.id
                JOIN categories cat ON o.category_id = cat.id
                WHERE c.name LIKE ? OR cat.name LIKE ? OR o.order_type LIKE ?
                ORDER BY o.created_at DESC
            """, (search_pattern, search_pattern, search_pattern))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"Error searching orders: {e}")
            return []
    
    def get_client_orders(self, client_id):
        """Get all orders for a specific client"""
        try:
            self.cursor.execute("""
                SELECT o.*, c.name as client_name, cat.name as category_name
                FROM orders o
                JOIN clients c ON o.client_id = c.id
                JOIN categories cat ON o.category_id = cat.id
                WHERE o.client_id = ?
                ORDER BY o.created_at DESC
            """, (client_id,))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching client orders: {e}")
            return []

    # Product operations
    def add_product(self, name: str, description: str, category_id: int,
                   sku: str = "", price: float = 0, stock_quantity: int = 0) -> Optional[int]:
        """إضافة منتج جديد"""
        try:
            self.cursor.execute('''
                INSERT INTO products (name, description, category_id, sku, price, stock_quantity)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (name, description, category_id, sku, price, stock_quantity))
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            self.logger.error(f"Error adding product: {e}")
            return None

    def get_all_products(self, active_only: bool = True) -> List[Dict]:
        """الحصول على جميع المنتجات"""
        try:
            query = '''
                SELECT p.*, c.name as category_name
                FROM products p
                JOIN categories c ON p.category_id = c.id
            '''
            if active_only:
                query += " WHERE p.is_active = 1"
            query += " ORDER BY p.name"

            self.cursor.execute(query)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            self.logger.error(f"Error fetching products: {e}")
            return []

    def search_products(self, search_text: str) -> List[Dict]:
        """البحث في المنتجات"""
        try:
            search_pattern = f"%{search_text}%"
            self.cursor.execute('''
                SELECT p.*, c.name as category_name
                FROM products p
                JOIN categories c ON p.category_id = c.id
                WHERE p.name LIKE ? OR p.sku LIKE ? OR p.description LIKE ?
                AND p.is_active = 1
                ORDER BY p.name
            ''', (search_pattern, search_pattern, search_pattern))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            self.logger.error(f"Error searching products: {e}")
            return []

    def update_product_stock(self, product_id: int, new_quantity: int) -> bool:
        """تحديث مخزون المنتج"""
        try:
            self.cursor.execute('''
                UPDATE products SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_quantity, product_id))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            self.logger.error(f"Error updating product stock: {e}")
            return False

    # Settings operations
    def get_setting(self, key: str, default_value: str = None) -> Optional[str]:
        """الحصول على إعداد"""
        try:
            self.cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            result = self.cursor.fetchone()
            return result['value'] if result else default_value
        except sqlite3.Error as e:
            self.logger.error(f"Error getting setting: {e}")
            return default_value

    def set_setting(self, key: str, value: str, description: str = "") -> bool:
        """تعيين إعداد"""
        try:
            self.cursor.execute('''
                INSERT OR REPLACE INTO settings (key, value, description, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (key, value, description))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            self.logger.error(f"Error setting value: {e}")
            return False

    # Audit operations
    def log_audit(self, table_name: str, record_id: int, action: str,
                 old_values: str = "", new_values: str = "", user_id: int = None) -> bool:
        """تسجيل عملية تدقيق"""
        try:
            self.cursor.execute('''
                INSERT INTO audit_log (table_name, record_id, action, old_values, new_values, user_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (table_name, record_id, action, old_values, new_values, user_id))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            self.logger.error(f"Error logging audit: {e}")
            return False