"""
مكتبة الرسوم المتحركة المخصصة
"""

from kivy.animation import Animation
from kivy.clock import Clock
from kivy.metrics import dp
from functools import partial


class AnimationManager:
    """مدير الرسوم المتحركة"""
    
    @staticmethod
    def fade_in(widget, duration=0.3, delay=0):
        """تأثير الظهور التدريجي"""
        widget.opacity = 0
        
        def start_animation(dt):
            anim = Animation(opacity=1, duration=duration, transition='out_cubic')
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def fade_out(widget, duration=0.3, callback=None):
        """تأثير الاختفاء التدريجي"""
        anim = Animation(opacity=0, duration=duration, transition='in_cubic')
        if callback:
            anim.bind(on_complete=lambda *args: callback())
        anim.start(widget)
    
    @staticmethod
    def slide_in_from_bottom(widget, duration=0.4, delay=0):
        """انزلاق من الأسفل"""
        original_y = widget.y
        widget.y = -widget.height
        widget.opacity = 0
        
        def start_animation(dt):
            anim = Animation(
                y=original_y,
                opacity=1,
                duration=duration,
                transition='out_back'
            )
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def slide_in_from_top(widget, duration=0.4, delay=0):
        """انزلاق من الأعلى"""
        original_y = widget.y
        widget.y = widget.parent.height
        widget.opacity = 0
        
        def start_animation(dt):
            anim = Animation(
                y=original_y,
                opacity=1,
                duration=duration,
                transition='out_back'
            )
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def slide_in_from_left(widget, duration=0.4, delay=0):
        """انزلاق من اليسار"""
        original_x = widget.x
        widget.x = -widget.width
        widget.opacity = 0
        
        def start_animation(dt):
            anim = Animation(
                x=original_x,
                opacity=1,
                duration=duration,
                transition='out_back'
            )
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def slide_in_from_right(widget, duration=0.4, delay=0):
        """انزلاق من اليمين"""
        original_x = widget.x
        widget.x = widget.parent.width
        widget.opacity = 0
        
        def start_animation(dt):
            anim = Animation(
                x=original_x,
                opacity=1,
                duration=duration,
                transition='out_back'
            )
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def scale_in(widget, duration=0.3, delay=0):
        """تكبير تدريجي"""
        widget.size = (0, 0)
        widget.opacity = 0
        original_size = widget.size
        
        def start_animation(dt):
            anim = Animation(
                size=original_size,
                opacity=1,
                duration=duration,
                transition='out_elastic'
            )
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def bounce_in(widget, duration=0.5, delay=0):
        """تأثير الارتداد"""
        widget.opacity = 0
        widget.size = (0, 0)
        original_size = widget.size
        
        def start_animation(dt):
            anim = Animation(
                size=original_size,
                opacity=1,
                duration=duration,
                transition='out_bounce'
            )
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def rotate_in(widget, duration=0.4, delay=0):
        """دوران مع الظهور"""
        widget.opacity = 0
        widget.rotation = 180
        
        def start_animation(dt):
            anim = Animation(
                rotation=0,
                opacity=1,
                duration=duration,
                transition='out_cubic'
            )
            anim.start(widget)
        
        if delay > 0:
            Clock.schedule_once(start_animation, delay)
        else:
            start_animation(0)
    
    @staticmethod
    def pulse(widget, scale=1.1, duration=0.2, repeat=False):
        """تأثير النبضة"""
        original_size = widget.size
        
        def pulse_animation():
            anim1 = Animation(
                size=(original_size[0] * scale, original_size[1] * scale),
                duration=duration,
                transition='out_cubic'
            )
            anim2 = Animation(
                size=original_size,
                duration=duration,
                transition='in_cubic'
            )
            
            sequence = anim1 + anim2
            
            if repeat:
                sequence.repeat = True
            
            sequence.start(widget)
        
        pulse_animation()
    
    @staticmethod
    def shake(widget, intensity=dp(5), duration=0.1, count=3):
        """تأثير الاهتزاز"""
        original_x = widget.x
        
        def shake_step(step):
            if step >= count * 2:
                # العودة للموضع الأصلي
                anim = Animation(x=original_x, duration=duration)
                anim.start(widget)
                return
            
            # تحديد الاتجاه
            direction = 1 if step % 2 == 0 else -1
            target_x = original_x + (intensity * direction)
            
            anim = Animation(x=target_x, duration=duration)
            anim.bind(on_complete=lambda *args: shake_step(step + 1))
            anim.start(widget)
        
        shake_step(0)
    
    @staticmethod
    def glow_effect(widget, color=(1, 1, 0, 0.5), duration=1.0):
        """تأثير التوهج"""
        # هذا التأثير يتطلب تخصيص أكثر تعقيداً
        # يمكن تنفيذه باستخدام Canvas instructions
        pass
    
    @staticmethod
    def stagger_animation(widgets, animation_func, delay_between=0.1):
        """تطبيق رسوم متحركة متتالية على مجموعة من العناصر"""
        for i, widget in enumerate(widgets):
            delay = i * delay_between
            animation_func(widget, delay=delay)
    
    @staticmethod
    def chain_animations(*animations):
        """ربط عدة رسوم متحركة في تسلسل"""
        if not animations:
            return
        
        first_anim = animations[0]
        
        for i in range(1, len(animations)):
            first_anim += animations[i]
        
        return first_anim
    
    @staticmethod
    def parallel_animations(*animations):
        """تشغيل عدة رسوم متحركة بالتوازي"""
        for anim in animations:
            anim.start()


class TransitionEffects:
    """تأثيرات الانتقال بين الشاشات"""
    
    @staticmethod
    def slide_transition(screen_manager, direction="left", duration=0.3):
        """انتقال انزلاقي"""
        from kivy.uix.screenmanager import SlideTransition
        screen_manager.transition = SlideTransition(direction=direction, duration=duration)
    
    @staticmethod
    def fade_transition(screen_manager, duration=0.3):
        """انتقال تدريجي"""
        from kivy.uix.screenmanager import FadeTransition
        screen_manager.transition = FadeTransition(duration=duration)
    
    @staticmethod
    def card_transition(screen_manager, direction="left", duration=0.3):
        """انتقال البطاقة"""
        from kivy.uix.screenmanager import CardTransition
        screen_manager.transition = CardTransition(direction=direction, duration=duration)
    
    @staticmethod
    def no_transition(screen_manager):
        """بدون انتقال"""
        from kivy.uix.screenmanager import NoTransition
        screen_manager.transition = NoTransition()


class LoadingAnimations:
    """رسوم متحركة للتحميل"""
    
    @staticmethod
    def spinning_circle(widget, duration=1.0):
        """دائرة دوارة"""
        anim = Animation(rotation=360, duration=duration)
        anim.repeat = True
        anim.start(widget)
        return anim
    
    @staticmethod
    def pulsing_dot(widget, scale_range=(0.5, 1.5), duration=0.8):
        """نقطة نابضة"""
        min_scale, max_scale = scale_range
        original_size = widget.size
        
        anim1 = Animation(
            size=(original_size[0] * max_scale, original_size[1] * max_scale),
            duration=duration / 2,
            transition='out_cubic'
        )
        anim2 = Animation(
            size=(original_size[0] * min_scale, original_size[1] * min_scale),
            duration=duration / 2,
            transition='in_cubic'
        )
        
        sequence = anim1 + anim2
        sequence.repeat = True
        sequence.start(widget)
        return sequence
    
    @staticmethod
    def wave_loading(widgets, duration=0.3, delay_between=0.1):
        """تحميل موجي"""
        def animate_widget(widget, delay):
            def start_anim(dt):
                anim = Animation(opacity=0.3, duration=duration) + \
                       Animation(opacity=1.0, duration=duration)
                anim.repeat = True
                anim.start(widget)
            
            Clock.schedule_once(start_anim, delay)
        
        for i, widget in enumerate(widgets):
            delay = i * delay_between
            animate_widget(widget, delay)


# إنشاء مثيل عام لمدير الرسوم المتحركة
anim_manager = AnimationManager()
transition_effects = TransitionEffects()
loading_animations = LoadingAnimations()
