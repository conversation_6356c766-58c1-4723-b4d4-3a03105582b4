"""
اختبارات نظام الأمان
"""

import pytest
import os
import tempfile
import sqlite3
from datetime import datetime, timedelta

from security import SecurityManager, UserManager
from database import DatabaseManager


class TestSecurityManager:
    """اختبارات مدير الأمان"""
    
    def setup_method(self):
        """إعداد الاختبار"""
        self.security = SecurityManager()
    
    def test_password_hashing(self):
        """اختبار تشفير كلمة المرور"""
        password = "test_password_123"
        hashed = self.security.hash_password(password)
        
        # التأكد من أن كلمة المرور مشفرة
        assert hashed != password
        assert len(hashed) > 0
        
        # التأكد من صحة التحقق
        assert self.security.verify_password(password, hashed)
        assert not self.security.verify_password("wrong_password", hashed)
    
    def test_session_management(self):
        """اختبار إدارة الجلسات"""
        user_id = 1
        username = "test_user"
        
        # إنشاء جلسة
        token = self.security.create_session(user_id, username)
        assert len(token) > 0
        
        # التحقق من الجلسة
        session = self.security.validate_session(token)
        assert session is not None
        assert session['user_id'] == user_id
        assert session['username'] == username
        
        # إلغاء الجلسة
        assert self.security.invalidate_session(token)
        assert self.security.validate_session(token) is None
    
    def test_account_lockout(self):
        """اختبار قفل الحساب"""
        username = "test_user"
        
        # التأكد من عدم قفل الحساب في البداية
        assert not self.security.is_account_locked(username)
        
        # تسجيل محاولات فاشلة
        for _ in range(5):
            self.security.record_failed_attempt(username)
        
        # التأكد من قفل الحساب
        assert self.security.is_account_locked(username)
        
        # إعادة تعيين المحاولات
        self.security.reset_failed_attempts(username)
        assert not self.security.is_account_locked(username)
    
    def test_password_strength_validation(self):
        """اختبار التحقق من قوة كلمة المرور"""
        # كلمة مرور ضعيفة
        weak_passwords = [
            "123",
            "password",
            "PASSWORD",
            "********",
            "Password",
            "Password1"
        ]
        
        for password in weak_passwords:
            is_strong, message = self.security.validate_password_strength(password)
            assert not is_strong
            assert len(message) > 0
        
        # كلمة مرور قوية
        strong_password = "StrongPass123!"
        is_strong, message = self.security.validate_password_strength(strong_password)
        assert is_strong
        assert "قوية" in message


class TestUserManager:
    """اختبارات مدير المستخدمين"""
    
    def setup_method(self):
        """إعداد الاختبار"""
        # إنشاء قاعدة بيانات مؤقتة
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.db = DatabaseManager(self.temp_db.name)
        self.user_manager = UserManager(self.db)
    
    def teardown_method(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        os.unlink(self.temp_db.name)
    
    def test_create_user(self):
        """اختبار إنشاء مستخدم"""
        username = "test_user"
        password = "TestPass123!"
        email = "<EMAIL>"
        full_name = "Test User"
        
        # إنشاء مستخدم
        success, message = self.user_manager.create_user(
            username, password, email, full_name
        )
        assert success
        assert "نجاح" in message
        
        # محاولة إنشاء مستخدم بنفس الاسم
        success, message = self.user_manager.create_user(
            username, password, email, full_name
        )
        assert not success
        assert "موجود" in message
    
    def test_authenticate_user(self):
        """اختبار مصادقة المستخدم"""
        username = "test_user"
        password = "TestPass123!"
        
        # إنشاء مستخدم
        self.user_manager.create_user(username, password)
        
        # مصادقة صحيحة
        success, message, user_data = self.user_manager.authenticate_user(
            username, password
        )
        assert success
        assert user_data is not None
        assert user_data['username'] == username
        
        # مصادقة خاطئة
        success, message, user_data = self.user_manager.authenticate_user(
            username, "wrong_password"
        )
        assert not success
        assert user_data is None
    
    def test_account_lockout_integration(self):
        """اختبار تكامل قفل الحساب"""
        username = "test_user"
        password = "TestPass123!"
        
        # إنشاء مستخدم
        self.user_manager.create_user(username, password)
        
        # محاولات دخول فاشلة متعددة
        for _ in range(5):
            success, message, user_data = self.user_manager.authenticate_user(
                username, "wrong_password"
            )
            assert not success
        
        # محاولة دخول بكلمة مرور صحيحة بعد القفل
        success, message, user_data = self.user_manager.authenticate_user(
            username, password
        )
        assert not success
        assert "مقفل" in message


class TestDatabaseSecurity:
    """اختبارات أمان قاعدة البيانات"""
    
    def setup_method(self):
        """إعداد الاختبار"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DatabaseManager(self.temp_db.name)
    
    def teardown_method(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        os.unlink(self.temp_db.name)
    
    def test_backup_restore(self):
        """اختبار النسخ الاحتياطي والاستعادة"""
        # إضافة بيانات تجريبية
        client_id = self.db.add_client("Test Client", "********9", "Test Address")
        assert client_id is not None
        
        # إنشاء نسخة احتياطية
        backup_file = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        backup_file.close()
        
        success = self.db.backup_database(backup_file.name)
        assert success
        
        # حذف البيانات الأصلية
        self.db.cursor.execute("DELETE FROM clients")
        self.db.conn.commit()
        
        # التأكد من حذف البيانات
        clients = self.db.get_all_clients()
        assert len(clients) == 0
        
        # استعادة من النسخة الاحتياطية
        success = self.db.restore_database(backup_file.name)
        assert success
        
        # التأكد من استعادة البيانات
        clients = self.db.get_all_clients()
        assert len(clients) == 1
        assert clients[0]['name'] == "Test Client"
        
        # تنظيف
        os.unlink(backup_file.name)


class TestPerformance:
    """اختبارات الأداء"""

    def test_memory_manager(self):
        """اختبار مدير الذاكرة"""
        from performance import MemoryManager

        memory_manager = MemoryManager()

        # اختبار الحصول على معلومات الذاكرة
        memory_info = memory_manager.get_memory_info()
        assert 'total_memory' in memory_info
        assert 'memory_percent' in memory_info

        # اختبار تنظيف الذاكرة
        memory_manager.cleanup_memory()  # يجب ألا يرفع استثناء

    def test_performance_monitor(self):
        """اختبار مراقب الأداء"""
        from performance import PerformanceMonitor

        monitor = PerformanceMonitor()

        @monitor.measure_time("test_function")
        def test_function():
            import time
            time.sleep(0.1)
            return "test"

        result = test_function()
        assert result == "test"

        # التحقق من تسجيل المقاييس
        summary = monitor.get_metrics_summary()
        assert "test_function" in summary
        assert summary["test_function"]["count"] == 1

    def test_cache_manager(self):
        """اختبار مدير التخزين المؤقت"""
        from performance import CacheManager

        cache_manager = CacheManager()

        # إنشاء cache
        test_cache = cache_manager.create_cache("test_cache", 10)

        # اختبار الـ cache
        def expensive_function(x):
            return x * 2

        result1 = test_cache(expensive_function, 5)
        result2 = test_cache(expensive_function, 5)

        assert result1 == result2 == 10

        # اختبار معلومات الـ cache
        cache_info = cache_manager.get_cache_info("test_cache")
        assert "hits" in cache_info
        assert "misses" in cache_info


class TestReports:
    """اختبارات التقارير"""

    def setup_method(self):
        """إعداد الاختبار"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DatabaseManager(self.temp_db.name)

        # إضافة بيانات تجريبية
        client_id = self.db.add_client("Test Client", "********9", "Test Address")
        category_id = self.db.add_category("Test Category", "Test Description")
        self.db.add_order(client_id, category_id, "Test Product", "Medium", 2, "Delivery")

    def teardown_method(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        os.unlink(self.temp_db.name)

    def test_report_generator(self):
        """اختبار مولد التقارير"""
        from reports import ReportGenerator

        report_gen = ReportGenerator(self.db)

        # اختبار ملخص المبيعات
        summary = report_gen.get_sales_summary()
        assert 'total_orders' in summary
        assert summary['total_orders'] >= 1

        # اختبار أفضل العملاء
        top_clients = report_gen.get_top_clients(5)
        assert isinstance(top_clients, list)

        # اختبار أداء الفئات
        category_performance = report_gen.get_category_performance()
        assert isinstance(category_performance, list)

    def test_comprehensive_report(self):
        """اختبار التقرير الشامل"""
        from reports import ReportGenerator

        report_gen = ReportGenerator(self.db)
        report_data = report_gen.generate_comprehensive_report()

        assert 'generated_at' in report_data
        assert 'sales_summary' in report_data
        assert 'top_clients' in report_data
        assert 'category_performance' in report_data


class TestNotifications:
    """اختبارات الإشعارات"""

    def setup_method(self):
        """إعداد الاختبار"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = DatabaseManager(self.temp_db.name)

    def teardown_method(self):
        """تنظيف بعد الاختبار"""
        self.db.close()
        os.unlink(self.temp_db.name)

    def test_notification_manager(self):
        """اختبار مدير الإشعارات"""
        from notifications import NotificationManager

        notification_manager = NotificationManager(self.db)

        # إضافة إشعار
        notification_id = notification_manager.add_notification(
            "Test Title",
            "Test Message",
            "info"
        )
        assert notification_id is not None

        # الحصول على الإشعارات
        notifications = notification_manager.get_notifications()
        assert len(notifications) >= 1
        assert notifications[0]['title'] == "Test Title"

        # تمييز كمقروء
        success = notification_manager.mark_as_read(notification_id)
        assert success

    def test_notification_templates(self):
        """اختبار قوالب الإشعارات"""
        from notifications import NotificationTemplates

        # اختبار قالب طلب جديد
        notification = NotificationTemplates.new_order_notification("Test Client", 123)
        assert notification['title'] == 'طلب جديد'
        assert 'Test Client' in notification['message']
        assert notification['data']['order_id'] == 123

        # اختبار قالب مخزون منخفض
        notification = NotificationTemplates.low_stock_notification("Test Product", 5)
        assert notification['type'] == 'warning'
        assert 'Test Product' in notification['message']


if __name__ == "__main__":
    pytest.main([__file__])
