from kivymd.uix.screen import MDScreen
from kivy.app import App
from kivy.metrics import dp
from kivy.garden.matplotlib.backend_kivyagg import FigureCanvasKivyAgg
from kivy.clock import Clock
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.tab import MDTabsBase
from kivymd.uix.floatlayout import MDFloatLayout
from kivymd.uix.button import MDFlatButton
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar import Snackbar
from kivymd.uix.spinner import MDSpinner

import matplotlib.pyplot as plt
import matplotlib as mpl
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading

from screens.loading import loading_indicator

# تكوين matplotlib للعمل مع اللغة العربية
import matplotlib.font_manager as fm
mpl.rcParams['font.family'] = 'sans-serif'
mpl.rcParams['axes.unicode_minus'] = False

class Tab(MDFloatLayout, MDTabsBase):
    """فئة لعلامات التبويب في شاشة الإحصائيات"""
    pass

class StatisticsScreen(MDScreen):
    """شاشة الإحصائيات والرسوم البيانية"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.app = App.get_running_app()
        self.db = self.app.db
        self.charts = {}
        self.current_tab = "orders"
        self.loading_dialog = None
        
    def on_enter(self):
        """يتم استدعاء هذه الدالة عند الدخول إلى الشاشة"""
        # تحميل البيانات وإنشاء الرسوم البيانية
        Clock.schedule_once(self.load_statistics)
    
    def on_leave(self):
        """يتم استدعاء هذه الدالة عند مغادرة الشاشة"""
        # تنظيف الرسوم البيانية لتوفير الذاكرة
        self.clear_charts()
    
    def on_tab_switch(self, instance_tabs, instance_tab, instance_tab_label, tab_text):
        """يتم استدعاء هذه الدالة عند تبديل علامة التبويب"""
        self.current_tab = tab_text.lower()
        # إعادة تحميل الرسوم البيانية للتبويب الجديد
        self.load_tab_charts(self.current_tab)
    
    def clear_charts(self):
        """إزالة جميع الرسوم البيانية وتحرير الذاكرة"""
        for container_id, chart_data in self.charts.items():
            container = self.ids.get(container_id)
            if container:
                container.clear_widgets()
        
        # تنظيف الذاكرة
        plt.close('all')
        self.charts = {}
    
    @loading_indicator
    def load_statistics(self, dt=None):
        """تحميل البيانات وإنشاء الرسوم البيانية"""
        # تحميل الرسوم البيانية للتبويب الحالي
        self.load_tab_charts(self.current_tab)
    
    def load_tab_charts(self, tab_name):
        """تحميل الرسوم البيانية لعلامة تبويب محددة"""
        # تنظيف الرسوم البيانية الحالية
        self.clear_charts()
        
        if tab_name == "orders":
            self.load_orders_charts()
        elif tab_name == "clients":
            self.load_clients_charts()
        elif tab_name == "categories":
            self.load_categories_charts()
    
    def load_orders_charts(self):
        """تحميل الرسوم البيانية للطلبات"""
        # الحصول على بيانات الطلبات
        orders = self.db.get_all_orders()
        
        if not orders:
            self.show_no_data_message("orders_chart_container")
            return
        
        # تحويل البيانات إلى DataFrame
        df_orders = pd.DataFrame(orders)
        
        # 1. رسم بياني للطلبات حسب الفئة
        self.create_orders_by_category_chart(df_orders, "orders_chart_container")
        
        # 2. رسم بياني للطلبات حسب النوع
        self.create_orders_by_type_chart(df_orders, "orders_type_chart_container")
    
    def load_clients_charts(self):
        """تحميل الرسوم البيانية للعملاء"""
        # الحصول على بيانات العملاء والطلبات
        clients = self.db.get_all_clients()
        orders = self.db.get_all_orders()
        
        if not clients or not orders:
            self.show_no_data_message("clients_chart_container")
            return
        
        # تحويل البيانات إلى DataFrame
        df_clients = pd.DataFrame(clients)
        df_orders = pd.DataFrame(orders)
        
        # 1. رسم بياني للعملاء الأكثر طلبًا
        self.create_top_clients_chart(df_orders, "clients_chart_container")
    
    def load_categories_charts(self):
        """تحميل الرسوم البيانية للفئات"""
        # الحصول على بيانات الفئات
        categories = self.db.get_all_categories()
        orders = self.db.get_all_orders()
        
        if not categories or not orders:
            self.show_no_data_message("categories_chart_container")
            return
        
        # تحويل البيانات إلى DataFrame
        df_categories = pd.DataFrame(categories)
        df_orders = pd.DataFrame(orders)
        
        # 1. رسم بياني للفئات الأكثر طلبًا
        self.create_categories_popularity_chart(df_orders, df_categories, "categories_chart_container")
    
    def create_orders_by_category_chart(self, df_orders, container_id):
        """إنشاء رسم بياني للطلبات حسب الفئة"""
        try:
            # تجميع البيانات حسب الفئة
            category_counts = df_orders['category_name'].value_counts()
            
            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(8, 6), dpi=80)
            category_counts.plot(kind='pie', autopct='%1.1f%%', ax=ax, startangle=90, 
                                shadow=True, explode=[0.05] * len(category_counts))
            ax.set_title('توزيع الطلبات حسب الفئة', fontsize=14)
            ax.set_ylabel('')
            
            # تطبيق نمط الألوان حسب وضع التطبيق
            self.apply_theme_to_chart(fig, ax)
            
            # إضافة الرسم البياني إلى الواجهة
            chart = FigureCanvasKivyAgg(figure=fig)
            container = self.ids.get(container_id)
            if container:
                container.clear_widgets()
                container.add_widget(chart)
            
            # حفظ مرجع للرسم البياني
            self.charts[container_id] = {'figure': fig, 'canvas': chart}
            
        except Exception as e:
            print(f"Error creating orders by category chart: {e}")
            self.show_error_message(container_id)
    
    def create_orders_by_type_chart(self, df_orders, container_id):
        """إنشاء رسم بياني للطلبات حسب النوع"""
        try:
            # تجميع البيانات حسب النوع
            type_counts = df_orders['order_type'].value_counts()
            
            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(8, 6), dpi=80)
            bars = ax.bar(type_counts.index, type_counts.values, color='royalblue')
            
            # إضافة القيم فوق الأعمدة
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:.0f}', ha='center', va='bottom')
            
            ax.set_title('عدد الطلبات حسب النوع', fontsize=14)
            ax.set_xlabel('نوع الطلب')
            ax.set_ylabel('عدد الطلبات')
            
            # تطبيق نمط الألوان حسب وضع التطبيق
            self.apply_theme_to_chart(fig, ax)
            
            # إضافة الرسم البياني إلى الواجهة
            chart = FigureCanvasKivyAgg(figure=fig)
            container = self.ids.get(container_id)
            if container:
                container.clear_widgets()
                container.add_widget(chart)
            
            # حفظ مرجع للرسم البياني
            self.charts[container_id] = {'figure': fig, 'canvas': chart}
            
        except Exception as e:
            print(f"Error creating orders by type chart: {e}")
            self.show_error_message(container_id)
    
    def create_top_clients_chart(self, df_orders, container_id):
        """إنشاء رسم بياني للعملاء الأكثر طلبًا"""
        try:
            # تجميع البيانات حسب العميل
            client_counts = df_orders['client_name'].value_counts().head(10)
            
            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(8, 6), dpi=80)
            bars = ax.barh(client_counts.index, client_counts.values, color='green')
            
            # إضافة القيم بجانب الأعمدة
            for bar in bars:
                width = bar.get_width()
                ax.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{width:.0f}', ha='left', va='center')
            
            ax.set_title('أفضل 10 عملاء من حيث عدد الطلبات', fontsize=14)
            ax.set_xlabel('عدد الطلبات')
            ax.set_ylabel('اسم العميل')
            
            # ترتيب البيانات تصاعديًا
            ax.invert_yaxis()
            
            # تطبيق نمط الألوان حسب وضع التطبيق
            self.apply_theme_to_chart(fig, ax)
            
            # إضافة الرسم البياني إلى الواجهة
            chart = FigureCanvasKivyAgg(figure=fig)
            container = self.ids.get(container_id)
            if container:
                container.clear_widgets()
                container.add_widget(chart)
            
            # حفظ مرجع للرسم البياني
            self.charts[container_id] = {'figure': fig, 'canvas': chart}
            
        except Exception as e:
            print(f"Error creating top clients chart: {e}")
            self.show_error_message(container_id)
    
    def create_categories_popularity_chart(self, df_orders, df_categories, container_id):
        """إنشاء رسم بياني لشعبية الفئات"""
        try:
            # تجميع البيانات حسب الفئة
            category_counts = df_orders['category_name'].value_counts()
            
            # إنشاء الرسم البياني
            fig, ax = plt.subplots(figsize=(8, 6), dpi=80)
            bars = ax.bar(category_counts.index, category_counts.values, color='purple')
            
            # إضافة القيم فوق الأعمدة
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:.0f}', ha='center', va='bottom')
            
            ax.set_title('شعبية الفئات حسب عدد الطلبات', fontsize=14)
            ax.set_xlabel('اسم الفئة')
            ax.set_ylabel('عدد الطلبات')
            
            # تدوير أسماء الفئات للقراءة بشكل أفضل
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            
            # تطبيق نمط الألوان حسب وضع التطبيق
            self.apply_theme_to_chart(fig, ax)
            
            # إضافة الرسم البياني إلى الواجهة
            chart = FigureCanvasKivyAgg(figure=fig)
            container = self.ids.get(container_id)
            if container:
                container.clear_widgets()
                container.add_widget(chart)
            
            # حفظ مرجع للرسم البياني
            self.charts[container_id] = {'figure': fig, 'canvas': chart}
            
        except Exception as e:
            print(f"Error creating categories popularity chart: {e}")
            self.show_error_message(container_id)
    
    def apply_theme_to_chart(self, fig, ax):
        """تطبيق نمط الألوان حسب وضع التطبيق"""
        # التحقق من وضع التطبيق (داكن أو فاتح)
        is_dark_theme = self.app.theme_cls.theme_style == "Dark"
        
        if is_dark_theme:
            # تطبيق نمط داكن
            fig.patch.set_facecolor('#303030')
            ax.set_facecolor('#303030')
            ax.tick_params(colors='white')
            ax.xaxis.label.set_color('white')
            ax.yaxis.label.set_color('white')
            ax.title.set_color('white')
            for text in ax.texts:
                text.set_color('white')
            
            # تغيير لون الحدود والشبكة
            for spine in ax.spines.values():
                spine.set_color('#BBBBBB')
            
            ax.grid(color='#BBBBBB', linestyle='-', linewidth=0.5, alpha=0.2)
        else:
            # تطبيق نمط فاتح (الافتراضي)
            fig.patch.set_facecolor('white')
            ax.set_facecolor('white')
            ax.grid(color='gray', linestyle='-', linewidth=0.5, alpha=0.2)
    
    def show_no_data_message(self, container_id):
        """عرض رسالة عندما لا تتوفر بيانات"""
        fig, ax = plt.subplots(figsize=(8, 6), dpi=80)
        ax.text(0.5, 0.5, 'لا توجد بيانات كافية لعرض الرسم البياني',
                horizontalalignment='center', verticalalignment='center',
                transform=ax.transAxes, fontsize=14)
        ax.axis('off')
        
        # تطبيق نمط الألوان حسب وضع التطبيق
        self.apply_theme_to_chart(fig, ax)
        
        # إضافة الرسم البياني إلى الواجهة
        chart = FigureCanvasKivyAgg(figure=fig)
        container = self.ids.get(container_id)
        if container:
            container.clear_widgets()
            container.add_widget(chart)
        
        # حفظ مرجع للرسم البياني
        self.charts[container_id] = {'figure': fig, 'canvas': chart}
    
    def show_error_message(self, container_id):
        """عرض رسالة خطأ عند فشل إنشاء الرسم البياني"""
        fig, ax = plt.subplots(figsize=(8, 6), dpi=80)
        ax.text(0.5, 0.5, 'حدث خطأ أثناء إنشاء الرسم البياني',
                horizontalalignment='center', verticalalignment='center',
                transform=ax.transAxes, fontsize=14, color='red')
        ax.axis('off')
        
        # تطبيق نمط الألوان حسب وضع التطبيق
        self.apply_theme_to_chart(fig, ax)
        
        # إضافة الرسم البياني إلى الواجهة
        chart = FigureCanvasKivyAgg(figure=fig)
        container = self.ids.get(container_id)
        if container:
            container.clear_widgets()
            container.add_widget(chart)
        
        # حفظ مرجع للرسم البياني
        self.charts[container_id] = {'figure': fig, 'canvas': chart}