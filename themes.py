"""
إدارة السمات والألوان المخصصة
"""

from kivymd.color_definitions import colors
from kivymd.theming import ThemableBehavior
from kivy.metrics import dp


class CustomTheme:
    """سمة مخصصة للتطبيق"""
    
    # ألوان مخصصة
    CUSTOM_COLORS = {
        "SalesBlue": {
            "50": "#E3F2FD",
            "100": "#BBDEFB", 
            "200": "#90CAF9",
            "300": "#64B5F6",
            "400": "#42A5F5",
            "500": "#2196F3",  # اللون الأساسي
            "600": "#1E88E5",
            "700": "#1976D2",
            "800": "#1565C0",
            "900": "#0D47A1",
            "A100": "#82B1FF",
            "A200": "#448AFF",
            "A400": "#2979FF",
            "A700": "#2962FF"
        },
        "SalesGreen": {
            "50": "#E8F5E8",
            "100": "#C8E6C9",
            "200": "#A5D6A7",
            "300": "#81C784",
            "400": "#66BB6A",
            "500": "#4CAF50",  # اللون الأساسي
            "600": "#43A047",
            "700": "#388E3C",
            "800": "#2E7D32",
            "900": "#1B5E20",
            "A100": "#B9F6CA",
            "A200": "#69F0AE",
            "A400": "#00E676",
            "A700": "#00C853"
        },
        "SalesOrange": {
            "50": "#FFF3E0",
            "100": "#FFE0B2",
            "200": "#FFCC80",
            "300": "#FFB74D",
            "400": "#FFA726",
            "500": "#FF9800",  # اللون الأساسي
            "600": "#FB8C00",
            "700": "#F57C00",
            "800": "#EF6C00",
            "900": "#E65100",
            "A100": "#FFD180",
            "A200": "#FFAB40",
            "A400": "#FF9100",
            "A700": "#FF6D00"
        }
    }
    
    # إعدادات السمة الفاتحة
    LIGHT_THEME = {
        "primary_color": CUSTOM_COLORS["SalesBlue"]["500"],
        "primary_light": CUSTOM_COLORS["SalesBlue"]["300"],
        "primary_dark": CUSTOM_COLORS["SalesBlue"]["700"],
        "accent_color": CUSTOM_COLORS["SalesOrange"]["500"],
        "bg_normal": "#FAFAFA",
        "bg_light": "#FFFFFF",
        "bg_dark": "#F5F5F5",
        "text_primary": "#212121",
        "text_secondary": "#757575",
        "divider_color": "#BDBDBD",
        "error_color": "#F44336",
        "success_color": CUSTOM_COLORS["SalesGreen"]["500"],
        "warning_color": "#FF9800"
    }
    
    # إعدادات السمة الداكنة
    DARK_THEME = {
        "primary_color": CUSTOM_COLORS["SalesBlue"]["400"],
        "primary_light": CUSTOM_COLORS["SalesBlue"]["200"],
        "primary_dark": CUSTOM_COLORS["SalesBlue"]["800"],
        "accent_color": CUSTOM_COLORS["SalesOrange"]["400"],
        "bg_normal": "#121212",
        "bg_light": "#1E1E1E",
        "bg_dark": "#000000",
        "text_primary": "#FFFFFF",
        "text_secondary": "#B3B3B3",
        "divider_color": "#373737",
        "error_color": "#CF6679",
        "success_color": CUSTOM_COLORS["SalesGreen"]["400"],
        "warning_color": "#FFB74D"
    }
    
    # أحجام الخطوط
    FONT_SIZES = {
        "H1": dp(96),
        "H2": dp(60),
        "H3": dp(48),
        "H4": dp(34),
        "H5": dp(24),
        "H6": dp(20),
        "Subtitle1": dp(16),
        "Subtitle2": dp(14),
        "Body1": dp(16),
        "Body2": dp(14),
        "Button": dp(14),
        "Caption": dp(12),
        "Overline": dp(10)
    }
    
    # المسافات والأبعاد
    SPACING = {
        "xs": dp(4),
        "sm": dp(8),
        "md": dp(16),
        "lg": dp(24),
        "xl": dp(32),
        "xxl": dp(48)
    }
    
    # أنصاف الأقطار
    RADIUS = {
        "small": dp(4),
        "medium": dp(8),
        "large": dp(12),
        "extra_large": dp(16),
        "round": dp(28)
    }
    
    # الظلال
    ELEVATION = {
        "none": 0,
        "low": 2,
        "medium": 4,
        "high": 8,
        "extra_high": 16
    }
    
    @classmethod
    def apply_theme(cls, app, theme_style="Light"):
        """تطبيق السمة على التطبيق"""
        theme_colors = cls.LIGHT_THEME if theme_style == "Light" else cls.DARK_THEME
        
        # تطبيق الألوان الأساسية
        app.theme_cls.theme_style = theme_style
        app.theme_cls.primary_palette = "Blue"  # سيتم استبداله بالألوان المخصصة
        app.theme_cls.accent_palette = "Orange"
        
        # تخصيص الألوان (إذا كان مدعوماً)
        try:
            app.theme_cls.primary_color = theme_colors["primary_color"]
            app.theme_cls.accent_color = theme_colors["accent_color"]
        except:
            pass
    
    @classmethod
    def get_color(cls, color_name, shade="500"):
        """الحصول على لون مخصص"""
        if color_name in cls.CUSTOM_COLORS:
            return cls.CUSTOM_COLORS[color_name].get(shade, cls.CUSTOM_COLORS[color_name]["500"])
        return colors.get(color_name, {}).get(shade, "#000000")


class AnimationConfig:
    """إعدادات الرسوم المتحركة"""
    
    # مدة الرسوم المتحركة
    DURATION = {
        "fast": 0.1,
        "normal": 0.2,
        "slow": 0.3,
        "extra_slow": 0.5
    }
    
    # منحنيات التسارع
    TRANSITION = {
        "ease_in": "in_quad",
        "ease_out": "out_quad", 
        "ease_in_out": "in_out_quad",
        "bounce": "out_bounce",
        "elastic": "out_elastic"
    }
    
    # تأثيرات الدخول
    ENTER_ANIMATIONS = {
        "fade_in": {"opacity": (0, 1)},
        "slide_up": {"y": ("-100dp", "0dp")},
        "slide_down": {"y": ("100dp", "0dp")},
        "slide_left": {"x": ("-100dp", "0dp")},
        "slide_right": {"x": ("100dp", "0dp")},
        "scale_up": {"size": ("0dp", "normal")},
        "rotate_in": {"rotation": (180, 0)}
    }
    
    # تأثيرات الخروج
    EXIT_ANIMATIONS = {
        "fade_out": {"opacity": (1, 0)},
        "slide_up": {"y": ("0dp", "100dp")},
        "slide_down": {"y": ("0dp", "-100dp")},
        "slide_left": {"x": ("0dp", "100dp")},
        "slide_right": {"x": ("0dp", "-100dp")},
        "scale_down": {"size": ("normal", "0dp")},
        "rotate_out": {"rotation": (0, 180)}
    }


class IconSet:
    """مجموعة الأيقونات المستخدمة في التطبيق"""
    
    # أيقونات التنقل
    NAVIGATION = {
        "home": "home",
        "clients": "account-group",
        "orders": "shopping",
        "categories": "tag-multiple",
        "statistics": "chart-line",
        "profile": "account",
        "settings": "cog",
        "logout": "logout"
    }
    
    # أيقونات الإجراءات
    ACTIONS = {
        "add": "plus",
        "edit": "pencil",
        "delete": "delete",
        "save": "content-save",
        "cancel": "close",
        "search": "magnify",
        "filter": "filter",
        "sort": "sort",
        "export": "export",
        "import": "import",
        "refresh": "refresh",
        "sync": "sync"
    }
    
    # أيقونات الحالة
    STATUS = {
        "success": "check-circle",
        "error": "alert-circle",
        "warning": "alert",
        "info": "information",
        "loading": "loading",
        "pending": "clock",
        "active": "check",
        "inactive": "close"
    }
    
    # أيقونات البيانات
    DATA = {
        "client": "account",
        "order": "shopping-outline",
        "category": "tag",
        "product": "package-variant",
        "phone": "phone",
        "email": "email",
        "address": "map-marker",
        "date": "calendar",
        "time": "clock",
        "money": "currency-usd",
        "quantity": "counter"
    }
    
    @classmethod
    def get_icon(cls, category, name):
        """الحصول على أيقونة"""
        category_icons = getattr(cls, category.upper(), {})
        return category_icons.get(name, "help-circle")


# إنشاء مثيل عام للسمة
custom_theme = CustomTheme()
animation_config = AnimationConfig()
icon_set = IconSet()
