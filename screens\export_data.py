import os
import pandas as pd
from datetime import datetime
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.snackbar import Snackbar
from kivymd.uix.filemanager import MDFileManager
from kivy.app import App
from kivy.metrics import dp
from kivy.properties import StringProperty
from kivymd.uix.list import OneLineIconListItem
from kivymd.uix.menu import MDDropdownMenu

from screens.loading import loading_indicator

class IconListItem(OneLineIconListItem):
    icon = StringProperty()

class ExportManager:
    """مدير تصدير البيانات إلى ملفات مختلفة"""
    
    def __init__(self):
        self.app = App.get_running_app()
        self.db = self.app.db
        self.file_manager = MDFileManager(
            exit_manager=self.exit_file_manager,
            select_path=self.select_export_path,
            preview=False,
        )
        self.export_type = None
        self.export_dialog = None
        self.export_menu = None
    
    def show_export_options(self, caller):
        """عرض قائمة بخيارات التصدير"""
        if not self.export_menu:
            menu_items = [
                {
                    "text": "تصدير العملاء",
                    "viewclass": "IconListItem",
                    "icon": "account-group",
                    "height": dp(56),
                    "on_release": lambda: self.prepare_export("clients"),
                },
                {
                    "text": "تصدير الطلبات",
                    "viewclass": "IconListItem",
                    "icon": "shopping",
                    "height": dp(56),
                    "on_release": lambda: self.prepare_export("orders"),
                },
                {
                    "text": "تصدير الفئات",
                    "viewclass": "IconListItem",
                    "icon": "tag-multiple",
                    "height": dp(56),
                    "on_release": lambda: self.prepare_export("categories"),
                },
                {
                    "text": "تصدير جميع البيانات",
                    "viewclass": "IconListItem",
                    "icon": "database-export",
                    "height": dp(56),
                    "on_release": lambda: self.prepare_export("all"),
                },
            ]
            self.export_menu = MDDropdownMenu(
                caller=caller,
                items=menu_items,
                width_mult=4,
            )
        self.export_menu.open()
    
    def prepare_export(self, export_type):
        """تحضير عملية التصدير"""
        if self.export_menu:
            self.export_menu.dismiss()
        
        self.export_type = export_type
        # فتح مدير الملفات لاختيار مسار الحفظ
        self.file_manager.show(os.path.expanduser("~"))
    
    def exit_file_manager(self, *args):
        """إغلاق مدير الملفات"""
        self.file_manager.close()
    
    def select_export_path(self, path):
        """تم اختيار مسار لحفظ ملف التصدير"""
        self.file_manager.close()
        
        # إنشاء اسم الملف بناءً على نوع التصدير والتاريخ
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.export_type}_{timestamp}.xlsx"
        full_path = os.path.join(path, filename)
        
        # بدء عملية التصدير
        self.export_data_to_excel(full_path)
    
    @loading_indicator
    def export_data_to_excel(self, filepath):
        """تصدير البيانات إلى ملف Excel"""
        try:
            writer = pd.ExcelWriter(filepath, engine='openpyxl')
            
            # تصدير البيانات حسب النوع المطلوب
            if self.export_type == "clients" or self.export_type == "all":
                clients = self.db.get_all_clients()
                df_clients = pd.DataFrame(clients)
                df_clients.to_excel(writer, sheet_name='العملاء', index=False)
            
            if self.export_type == "orders" or self.export_type == "all":
                orders = self.db.get_all_orders()
                df_orders = pd.DataFrame(orders)
                df_orders.to_excel(writer, sheet_name='الطلبات', index=False)
            
            if self.export_type == "categories" or self.export_type == "all":
                categories = self.db.get_all_categories()
                df_categories = pd.DataFrame(categories)
                df_categories.to_excel(writer, sheet_name='الفئات', index=False)
            
            writer.close()
            
            # عرض رسالة نجاح
            Snackbar(
                text=f"تم تصدير البيانات بنجاح إلى {filepath}",
                duration=5,
            ).open()
            
        except Exception as e:
            # عرض رسالة خطأ
            Snackbar(
                text=f"حدث خطأ أثناء التصدير: {str(e)}",
                duration=5,
            ).open()