import os
import json
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.properties import ObjectProperty
from kivy.clock import Clock
from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.bottomnavigation import MDBottomNavigation
from kivymd.uix.list import OneLineListItem, TwoLineListItem
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.snackbar import Snackbar
from functools import partial

from database import DatabaseManager
from screens.utils import debounce
from screens.splash_screen import SplashScreen
from screens.profile_screen import ProfileScreen
from screens.statistics_screen import StatisticsScreen
from screens.export_data import ExportManager

# Load all KV files
for kv_file in os.listdir('screens'):
    if kv_file.endswith('.kv'):
        Builder.load_file(os.path.join('screens', kv_file))

# Login Screen
class LoginScreen(MDScreen):
    def verify_credentials(self):
        username = self.ids.username.text
        password = self.ids.password.text
        
        # Simple mock authentication
        if username and password:  # Just check if fields are not empty
            self.manager.current = 'main'
            self.ids.username.text = ""
            self.ids.password.text = ""
            Snackbar(text="تم تسجيل الدخول بنجاح").open()
        else:
            Snackbar(text="الرجاء إدخال اسم المستخدم وكلمة المرور").open()

# Main Screen with Bottom Navigation
class MainScreen(MDScreen):
    pass

# Client Screen
class ClientsScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create debounced search function
        self.debounced_search = debounce(0.3)(self._perform_search)
    
    def on_enter(self):
        self.load_clients()
        # Set up search functionality
        self.ids.search_client.bind(text=self.on_search_text_change)
    
    def load_clients(self):
        clients_list = self.ids.clients_list
        clients_list.clear_widgets()
        
        db = MDApp.get_running_app().db
        clients = db.get_all_clients()
        
        if not clients:
            clients_list.add_widget(OneLineListItem(text="لم يتم العثور على عملاء"))
            return
            
        for client in clients:
            item = TwoLineListItem(
                text=client['name'],
                secondary_text=f"الهاتف: {client['phone']} | العنوان: {client['address']}",
                on_release=lambda x, client_id=client['id']: self.show_client_details(client_id)
            )
            clients_list.add_widget(item)
    
    def on_search_text_change(self, instance, value):
        # Use debounced search to avoid excessive database queries
        self.debounced_search(value)
    
    def _perform_search(self, value):
        # Import here to avoid circular imports
        from screens.client_search import search_clients
        
        db = MDApp.get_running_app().db
        search_clients(self.ids.clients_list, value, db)
    
    def show_client_details(self, client_id):
        # Implement client details view
        client = MDApp.get_running_app().db.get_client_by_id(client_id)
        if client:
            dialog = MDDialog(
                title=f"العميل: {client['name']}",
                text=f"الهاتف: {client['phone']}\nالعنوان: {client['address']}\nالبريد الإلكتروني: {client['email'] or 'غير متوفر'}",
                buttons=[
                    MDFlatButton(
                        text="إغلاق",
                        on_release=lambda x: dialog.dismiss()
                    ),
                    MDFlatButton(
                        text="تعديل",
                        on_release=lambda x: (dialog.dismiss(), self.edit_client(client['id']))
                    ),
                ],
            )
            dialog.open()
    
    def edit_client(self, client_id):
        # Store the client_id for editing
        app = MDApp.get_running_app()
        app.client_to_edit = client_id
        
        # Get client data
        client = app.db.get_client_by_id(client_id)
        
        # Navigate to edit screen
        edit_screen = app.root.ids.screen_manager.get_screen('add_client')
        
        # Populate fields with client data
        edit_screen.ids.name.text = client['name']
        edit_screen.ids.phone.text = client['phone']
        edit_screen.ids.address.text = client['address']
        edit_screen.ids.email.text = client['email'] or ''
        
        # Change screen title and button text
        edit_screen.ids.top_app_bar.title = "تعديل بيانات العميل"
        edit_screen.ids.save_button.text = "تحديث"
        
        # Navigate to edit screen
        self.manager.current = 'add_client'
    
    def show_add_client_dialog(self):
        self.manager.current = 'add_client'

# Add Client Screen
class AddClientScreen(MDScreen):
    def on_enter(self):
        # Check if we're in edit mode or add mode
        app = MDApp.get_running_app()
        if not hasattr(app, 'client_to_edit'):
            # Reset to add mode
            self.ids.top_app_bar.title = "إضافة عميل جديد"
            self.ids.save_button.text = "حفظ"
            self.clear_fields()
    
    def save_client(self):
        name = self.ids.name.text
        phone = self.ids.phone.text
        address = self.ids.address.text
        email = self.ids.email.text
        
        # Validate form inputs
        from screens.validation import validate_client_form
        is_valid, error_message = validate_client_form(name, phone, address, email)
        
        if not is_valid:
            Snackbar(text=error_message).open()
            return
        
        app = MDApp.get_running_app()
        db = app.db
        
        # Check if we're editing or adding
        if hasattr(app, 'client_to_edit') and app.client_to_edit:
            # Update existing client
            success = db.update_client(app.client_to_edit, name, phone, address, email)
            if success:
                Snackbar(text="تم تحديث بيانات العميل بنجاح").open()
                # Reset edit mode
                app.client_to_edit = None
                self.clear_fields()
                self.manager.current = 'main'
            else:
                Snackbar(text="فشل في تحديث بيانات العميل").open()
        else:
            # Add new client
            client_id = db.add_client(name, phone, address, email)
            if client_id:
                Snackbar(text="تمت إضافة العميل بنجاح").open()
                self.clear_fields()
                self.manager.current = 'main'
            else:
                Snackbar(text="فشل في إضافة العميل").open()
    
    def clear_fields(self):
        self.ids.name.text = ""
        self.ids.phone.text = ""
        self.ids.address.text = ""
        self.ids.email.text = ""
    
    def cancel(self):
        # Reset edit mode if it exists
        app = MDApp.get_running_app()
        if hasattr(app, 'client_to_edit'):
            app.client_to_edit = None
        
        self.clear_fields()
        self.manager.current = 'main'

# Orders Screen
class OrdersScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create debounced search function
        self.debounced_search = debounce(0.3)(self._perform_search)
    
    def on_enter(self):
        self.load_orders()
        # Set up search functionality
        self.ids.search_order.bind(text=self.on_search_text_change)
    
    def load_orders(self):
        orders_list = self.ids.orders_list
        orders_list.clear_widgets()
        
        db = MDApp.get_running_app().db
        orders = db.get_all_orders()
        
        if not orders:
            orders_list.add_widget(OneLineListItem(text="لم يتم العثور على طلبات"))
            return
            
        for order in orders:
            client_name = order['client_name']
            category_name = order['category_name']
            item = TwoLineListItem(
                text=f"طلب #{order['id']} - {client_name}",
                secondary_text=f"الفئة: {category_name} | النوع: {order['order_type']} | الكمية: {order['quantity']}"
            )
            orders_list.add_widget(item)
    
    def on_search_text_change(self, instance, value):
        # Use debounced search to avoid excessive database queries
        self.debounced_search(value)
    
    def _perform_search(self, value):
        # Import here to avoid circular imports
        from screens.order_search import search_orders
        
        db = MDApp.get_running_app().db
        search_orders(self.ids.orders_list, value, db)
    
    def show_add_order_dialog(self):
        self.manager.current = 'add_order'

# Add Order Screen
class AddOrderScreen(MDScreen):
    def on_enter(self):
        self.load_clients_dropdown()
        self.load_categories_dropdown()
    
    def load_clients_dropdown(self):
        clients_dropdown = self.ids.client_id
        clients_dropdown.items = []
        
        db = MDApp.get_running_app().db
        clients = db.get_all_clients()
        
        if clients:
            clients_dropdown.items = [{"viewclass": "OneLineListItem", 
                                      "text": client['name'], 
                                      "on_release": lambda x=client['id'], name=client['name']: self.set_client_item(x, name)} 
                                     for client in clients]
    
    def load_categories_dropdown(self):
        categories_dropdown = self.ids.category_id
        categories_dropdown.items = []
        
        db = MDApp.get_running_app().db
        categories = db.get_all_categories()
        
        if categories:
            categories_dropdown.items = [{"viewclass": "OneLineListItem", 
                                        "text": category['name'], 
                                        "on_release": lambda x=category['id'], name=category['name']: self.set_category_item(x, name)} 
                                       for category in categories]
    
    def set_client_item(self, client_id, client_name):
        self.ids.client_id.text = client_name
        self.client_id_value = client_id
        self.ids.client_id.dismiss()
    
    def set_category_item(self, category_id, category_name):
        self.ids.category_id.text = category_name
        self.category_id_value = category_id
        self.ids.category_id.dismiss()
    
    def save_order(self):
        try:
            client_id = getattr(self, 'client_id_value', None)
            category_id = getattr(self, 'category_id_value', None)
            order_type = self.ids.order_type.text
            size = self.ids.size.text
            quantity = self.ids.quantity.text
            delivery_method = self.ids.delivery_method.text
            notes = self.ids.notes.text
            
            # Validate form inputs
            from screens.validation import validate_order_form
            is_valid, error_message = validate_order_form(
                client_id, category_id, order_type, size, quantity, delivery_method
            )
            
            if not is_valid:
                Snackbar(text=error_message).open()
                return
                
            db = MDApp.get_running_app().db
            order_id = db.add_order(
                client_id, category_id, order_type, size, 
                int(quantity), delivery_method, notes
            )
            
            if order_id:
                Snackbar(text="تمت إضافة الطلب بنجاح").open()
                self.clear_fields()
                self.manager.current = 'main'
            else:
                Snackbar(text="فشل في إضافة الطلب").open()
        except Exception as e:
            Snackbar(text=f"خطأ: {str(e)}").open()
    
    def clear_fields(self):
        self.ids.client_id.text = "اختر العميل"
        self.ids.category_id.text = "اختر الفئة"
        self.ids.order_type.text = ""
        self.ids.size.text = ""
        self.ids.quantity.text = ""
        self.ids.delivery_method.text = ""
        self.ids.notes.text = ""
        if hasattr(self, 'client_id_value'):
            del self.client_id_value
        if hasattr(self, 'category_id_value'):
            del self.category_id_value
    
    def cancel(self):
        self.clear_fields()
        self.manager.current = 'main'

# Categories Screen
class CategoriesScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create debounced search function
        self.debounced_search = debounce(0.3)(self._perform_search)
    
    def on_enter(self):
        self.load_categories()
        # Set up search functionality
        self.ids.search_category.bind(text=self.on_search_text_change)
    
    def load_categories(self):
        categories_list = self.ids.categories_list
        categories_list.clear_widgets()
        
        db = MDApp.get_running_app().db
        categories = db.get_all_categories()
        
        if not categories:
            categories_list.add_widget(OneLineListItem(text="لم يتم العثور على فئات"))
            return
            
        for category in categories:
            item = TwoLineListItem(
                text=category['name'],
                secondary_text=category['description'] if category['description'] else "لا يوجد وصف",
                on_release=lambda x, cat_id=category['id']: self.show_category_dialog(cat_id)
            )
            categories_list.add_widget(item)
    
    def on_search_text_change(self, instance, value):
        # Use debounced search to avoid excessive database queries
        self.debounced_search(value)
    
    def _perform_search(self, value):
        # Import here to avoid circular imports
        from screens.category_search import search_categories
        
        db = MDApp.get_running_app().db
        search_categories(self.ids.categories_list, value, db)
    
    def show_category_dialog(self, category_id):
        # Get category details from database
        db = MDApp.get_running_app().db
        # Use the cursor directly to avoid caching issues
        category = db.cursor.execute(
            "SELECT * FROM categories WHERE id = ?", (category_id,)
        ).fetchone()
        
        if category:
            # Convert to dict for easier access
            category = dict(category)
            
            dialog_content = MDBoxLayout(
                orientation="vertical",
                spacing="12dp",
                size_hint_y=None,
                height="120dp"
            )
            
            dialog = MDDialog(
                title=f"الفئة: {category['name']}",
                text=f"الوصف: {category['description'] if category['description'] else 'لا يوجد وصف'}",
                buttons=[
                    MDFlatButton(
                        text="تعديل",
                        on_release=lambda x: self.edit_category(category_id, dialog)
                    ),
                    MDFlatButton(
                        text="حذف",
                        on_release=lambda x: self.delete_category(category_id, dialog)
                    ),
                    MDFlatButton(
                        text="إغلاق",
                        on_release=lambda x: dialog.dismiss()
                    ),
                ],
            )
            dialog.open()
    
    def edit_category(self, category_id, dialog):
        dialog.dismiss()
        # Implement edit functionality
        Snackbar(text=f"تعديل الفئة {category_id}").open()
    
    def delete_category(self, category_id, dialog):
        dialog.dismiss()
        db = MDApp.get_running_app().db
        success, message = db.delete_category(category_id)
        if success:
            Snackbar(text=message).open()
            self.load_categories()
        else:
            Snackbar(text=message).open()
    
    def show_add_category_dialog(self):
        self.manager.current = 'add_category'

# Add Category Screen
class AddCategoryScreen(MDScreen):
    def save_category(self):
        name = self.ids.name.text
        description = self.ids.description.text
        
        # Validate form inputs
        from screens.validation import validate_category_form
        is_valid, error_message = validate_category_form(name)
        
        if not is_valid:
            Snackbar(text=error_message).open()
            return
            
        db = MDApp.get_running_app().db
        category_id = db.add_category(name, description)
        
        if category_id:
            Snackbar(text="تمت إضافة الفئة بنجاح").open()
            self.clear_fields()
            self.manager.current = 'main'
        else:
            Snackbar(text="فشل في إضافة الفئة").open()
    
    def clear_fields(self):
        self.ids.name.text = ""
        self.ids.description.text = ""
    
    def cancel(self):
        self.clear_fields()
        self.manager.current = 'main'

# تم نقل فئة ProfileScreen إلى ملف screens/profile_screen.py

# Main App Class
class SalesRepApp(MDApp):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.export_manager = None
    
    def build(self):
        # Set default theme
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.theme_style = "Light"
        
        # Load user preferences if available
        self.load_user_preferences()
        
        # Initialize database
        self.db = DatabaseManager()
        
        # Initialize export manager
        self.export_manager = ExportManager()
        
        # Initialize UI
        self.ui = Builder.load_file('main.kv')
        
        # Schedule data initialization for the next frame to avoid UI blocking
        Clock.schedule_once(self.initialize_data)
        
        return self.ui
        
    def load_user_preferences(self):
        """Load user preferences from storage"""
        try:
            # Check if preferences file exists
            if os.path.exists('user_prefs.json'):
                with open('user_prefs.json', 'r') as f:
                    prefs = json.load(f)
                    
                # Apply theme preferences
                if 'theme_style' in prefs:
                    self.theme_cls.theme_style = prefs['theme_style']
                if 'primary_palette' in prefs:
                    self.theme_cls.primary_palette = prefs['primary_palette']
                if 'accent_palette' in prefs:
                    self.theme_cls.accent_palette = prefs['accent_palette']
        except Exception as e:
            print(f"Error loading preferences: {e}")
    
    def save_user_preferences(self):
        """Save user preferences to storage"""
        try:
            prefs = {
                'theme_style': self.theme_cls.theme_style,
                'primary_palette': self.theme_cls.primary_palette,
                'accent_palette': self.theme_cls.accent_palette
            }
            
            with open('user_prefs.json', 'w') as f:
                json.dump(prefs, f)
        except Exception as e:
            print(f"Error saving preferences: {e}")
    
    def toggle_theme_style(self):
        """Toggle between light and dark theme"""
        self.theme_cls.theme_style = (
            "Dark" if self.theme_cls.theme_style == "Light" else "Light"
        )
        self.save_user_preferences()
    
    def initialize_data(self, dt):
        # Import here to avoid circular imports
        from screens.loading import LoadingDialog
        
        # Show loading dialog
        loading = LoadingDialog(title="جاري تهيئة البيانات...")
        loading.show()
        
        # Add sample data in a separate function
        def add_data(*args):
            self.add_sample_data()
            loading.dismiss()
        
        # Schedule data addition for the next frame
        Clock.schedule_once(add_data)
    
    def add_sample_data(self):
        # Check if there are any categories
        categories = self.db.get_all_categories()
        if not categories:
            # Add sample categories
            self.db.add_category("إلكترونيات", "منتجات إلكترونية متنوعة")
            self.db.add_category("ملابس", "ملابس رجالية ونسائية")
            self.db.add_category("أغذية", "منتجات غذائية متنوعة")
            self.db.add_category("أثاث", "أثاث منزلي ومكتبي")
            self.db.add_category("مستلزمات منزلية", "أدوات ومستلزمات منزلية")
            print("تمت إضافة فئات نموذجية")
        
        # Check if there are any clients
        clients = self.db.get_all_clients()
        if not clients:
            # Add sample clients
            self.db.add_client("أحمد محمد", "0501234567", "الرياض - حي النزهة", "<EMAIL>")
            self.db.add_client("سارة عبدالله", "0551234567", "جدة - حي الروضة", "<EMAIL>")
            self.db.add_client("محمد علي", "0561234567", "الدمام - حي الشاطئ", "<EMAIL>")
            self.db.add_client("نورة سعد", "0531234567", "الرياض - حي الملقا", "<EMAIL>")
            print("تمت إضافة عملاء نموذجيين")
            
            # Add sample orders if clients exist
            clients = self.db.get_all_clients()
            categories = self.db.get_all_categories()
            if clients and categories:
                client_ids = [client['id'] for client in clients]
                category_ids = [category['id'] for category in categories]
                
                # Add some sample orders
                self.db.add_order(client_ids[0], category_ids[0], "جهاز", "كبير", 2, "توصيل")
                self.db.add_order(client_ids[1], category_ids[1], "قميص", "متوسط", 3, "استلام")
                self.db.add_order(client_ids[2], category_ids[2], "عصير", "صغير", 5, "توصيل")
                self.db.add_order(client_ids[0], category_ids[3], "طاولة", "كبير", 1, "توصيل")
                self.db.add_order(client_ids[3], category_ids[4], "مكنسة", "متوسط", 1, "استلام")
                print("تمت إضافة طلبات نموذجية")

    def show_export_options(self, caller):
        """عرض خيارات تصدير البيانات"""
        if self.export_manager:
            self.export_manager.show_export_options(caller)

if __name__ == '__main__':
    # Set window size to simulate mobile phone dimensions
    Window.size = (400, 700)
    
    # Disable multitouch emulation (improves performance)
    from kivy.config import Config
    Config.set('input', 'mouse', 'mouse,multitouch_on_demand')
    
    # Set window title
    Window.set_title("أداة مندوب المبيعات")
    
    # Run the app
    SalesRepApp().run()