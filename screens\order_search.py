from kivymd.uix.list import TwoLineListItem, OneLineListItem

def search_orders(orders_list_widget, search_text, db=None):
    """
    Search orders based on search text and update the orders list widget
    
    Args:
        orders_list_widget: The MDList widget to update
        search_text: Text to search for in client names, order types, or categories
        db: Database manager instance
    """
    # Clear the current list
    orders_list_widget.clear_widgets()
    
    if not search_text.strip():
        # If search text is empty, show all orders
        orders = db.get_all_orders()
    else:
        # Use optimized database search
        orders = db.search_orders(search_text)
    
    if not orders:
        orders_list_widget.add_widget(OneLineListItem(text="No orders found"))
        return
    
    # Add orders to the list
    for order in orders:
        client_name = order['client_name']
        category_name = order['category_name']
        item = TwoLineListItem(
            text=f"Order #{order['id']} - {client_name}",
            secondary_text=f"Category: {category_name} | Type: {order['order_type']} | Quantity: {order['quantity']}"
        )
        orders_list_widget.add_widget(item) 