from kivymd.uix.list import TwoLine<PERSON>istItem, OneLineListItem

def search_clients(clients_list_widget, search_text, db=None):
    """
    Search clients based on search text and update the clients list widget
    
    Args:
        clients_list_widget: The MDList widget to update
        search_text: Text to search for in client names, phones, or addresses
        db: Database manager instance
    """
    # Clear the current list
    clients_list_widget.clear_widgets()
    
    if not search_text.strip():
        # If search text is empty, show all clients
        clients = db.get_all_clients()
    else:
        # Use optimized database search
        clients = db.search_clients(search_text)
    
    if not clients:
        clients_list_widget.add_widget(OneLineListItem(text="No clients found"))
        return
    
    # Add filtered clients to the list
    for client in clients:
        item = TwoLineListItem(
            text=client['name'],
            secondary_text=f"Phone: {client['phone']} | Address: {client['address']}",
            on_release=lambda x, client_id=client['id']: x.parent.parent.parent.parent.show_client_details(client_id)
        )
        clients_list_widget.add_widget(item) 