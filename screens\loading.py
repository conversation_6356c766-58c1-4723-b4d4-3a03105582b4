from kivymd.uix.dialog import MDDialog
from kivymd.uix.spinner import MDSpinner
from kivymd.uix.boxlayout import MDBoxLayout
from kivy.clock import Clock

class LoadingDialog:
    """
    A loading indicator dialog that can be shown during long operations
    """
    def __init__(self, title="جاري التحميل..."):
        self.title = title
        self.dialog = None
    
    def show(self):
        """Show the loading dialog"""
        if self.dialog is None:
            # Create layout for spinner
            box = MDBoxLayout(
                orientation='vertical',
                padding=10,
                adaptive_height=True
            )
            
            # Add spinner to layout
            spinner = MDSpinner(
                size_hint=(None, None),
                size=(46, 46),
                pos_hint={'center_x': .5, 'center_y': .5}
            )
            box.add_widget(spinner)
            
            # Create dialog
            self.dialog = MDDialog(
                title=self.title,
                type="custom",
                content_cls=box,
                auto_dismiss=False
            )
        
        # Show dialog on next frame to avoid UI blocking
        Clock.schedule_once(lambda dt: self.dialog.open())
    
    def dismiss(self):
        """Dismiss the loading dialog"""
        if self.dialog:
            # Dismiss on next frame to avoid UI blocking
            Clock.schedule_once(lambda dt: self.dialog.dismiss())

def show_loading(func):
    """
    Decorator to show loading indicator during function execution
    
    Args:
        func: Function to decorate
    """
    def wrapper(*args, **kwargs):
        loading = LoadingDialog()
        loading.show()
        
        try:
            result = func(*args, **kwargs)
            loading.dismiss()
            return result
        except Exception as e:
            loading.dismiss()
            raise e
    
    return wrapper