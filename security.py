"""
Security utilities for the Sales Rep Tool
Provides authentication, password hashing, and session management
"""

import hashlib
import secrets
import bcrypt
import json
import os
from datetime import datetime, timedelta
from cryptography.fernet import <PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any


class SecurityManager:
    """مدير الأمان للتطبيق"""
    
    def __init__(self):
        self.secret_key = self._get_or_create_secret_key()
        self.cipher_suite = Fernet(self.secret_key)
        self.sessions = {}
        self.failed_attempts = {}
        self.max_failed_attempts = 5
        self.lockout_duration = 300  # 5 minutes
        
    def _get_or_create_secret_key(self) -> bytes:
        """إنشاء أو استرداد المفتاح السري"""
        key_file = 'secret.key'
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور باستخدام bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """التحقق من كلمة المرور"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات"""
        encrypted = self.cipher_suite.encrypt(data.encode())
        return encrypted.decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        decrypted = self.cipher_suite.decrypt(encrypted_data.encode())
        return decrypted.decode()
    
    def generate_session_token(self) -> str:
        """إنشاء رمز جلسة آمن"""
        return secrets.token_urlsafe(32)
    
    def create_session(self, user_id: int, username: str) -> str:
        """إنشاء جلسة جديدة"""
        token = self.generate_session_token()
        self.sessions[token] = {
            'user_id': user_id,
            'username': username,
            'created_at': datetime.now(),
            'last_activity': datetime.now()
        }
        return token
    
    def validate_session(self, token: str) -> Optional[Dict[str, Any]]:
        """التحقق من صحة الجلسة"""
        if token not in self.sessions:
            return None
            
        session = self.sessions[token]
        
        # التحقق من انتهاء صلاحية الجلسة (24 ساعة)
        if datetime.now() - session['created_at'] > timedelta(hours=24):
            del self.sessions[token]
            return None
            
        # تحديث آخر نشاط
        session['last_activity'] = datetime.now()
        return session
    
    def invalidate_session(self, token: str) -> bool:
        """إلغاء الجلسة"""
        if token in self.sessions:
            del self.sessions[token]
            return True
        return False
    
    def is_account_locked(self, username: str) -> bool:
        """التحقق من قفل الحساب"""
        if username not in self.failed_attempts:
            return False
            
        attempts = self.failed_attempts[username]
        if attempts['count'] >= self.max_failed_attempts:
            # التحقق من انتهاء فترة القفل
            if datetime.now() - attempts['last_attempt'] < timedelta(seconds=self.lockout_duration):
                return True
            else:
                # إعادة تعيين المحاولات الفاشلة
                del self.failed_attempts[username]
                return False
        return False
    
    def record_failed_attempt(self, username: str):
        """تسجيل محاولة دخول فاشلة"""
        if username not in self.failed_attempts:
            self.failed_attempts[username] = {'count': 0, 'last_attempt': datetime.now()}
        
        self.failed_attempts[username]['count'] += 1
        self.failed_attempts[username]['last_attempt'] = datetime.now()
    
    def reset_failed_attempts(self, username: str):
        """إعادة تعيين المحاولات الفاشلة"""
        if username in self.failed_attempts:
            del self.failed_attempts[username]
    
    def validate_password_strength(self, password: str) -> tuple[bool, str]:
        """التحقق من قوة كلمة المرور"""
        if len(password) < 8:
            return False, "كلمة المرور يجب أن تكون 8 أحرف على الأقل"
        
        if not any(c.isupper() for c in password):
            return False, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"
        
        if not any(c.islower() for c in password):
            return False, "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل"
        
        if not any(c.isdigit() for c in password):
            return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"
        
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            return False, "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل"
        
        return True, "كلمة مرور قوية"


class UserManager:
    """مدير المستخدمين"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        self.security = SecurityManager()
        self._create_users_table()
        self._create_default_admin()
    
    def _create_users_table(self):
        """إنشاء جدول المستخدمين"""
        try:
            self.db.cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT,
                    full_name TEXT,
                    role TEXT DEFAULT 'user',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            self.db.conn.commit()
        except Exception as e:
            print(f"Error creating users table: {e}")
    
    def _create_default_admin(self):
        """إنشاء مستخدم إداري افتراضي"""
        try:
            # التحقق من وجود مستخدم إداري
            self.db.cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            admin_count = self.db.cursor.fetchone()[0]
            
            if admin_count == 0:
                # إنشاء مستخدم إداري افتراضي
                default_password = "Admin@123"
                password_hash = self.security.hash_password(default_password)
                
                self.db.cursor.execute('''
                    INSERT INTO users (username, password_hash, email, full_name, role)
                    VALUES (?, ?, ?, ?, ?)
                ''', ("admin", password_hash, "<EMAIL>", "مدير النظام", "admin"))
                self.db.conn.commit()
                print("تم إنشاء المستخدم الإداري الافتراضي: admin / Admin@123")
        except Exception as e:
            print(f"Error creating default admin: {e}")
    
    def authenticate_user(self, username: str, password: str) -> tuple[bool, str, Optional[Dict]]:
        """مصادقة المستخدم"""
        # التحقق من قفل الحساب
        if self.security.is_account_locked(username):
            return False, "الحساب مقفل مؤقتاً بسبب المحاولات الفاشلة المتكررة", None
        
        try:
            self.db.cursor.execute('''
                SELECT id, username, password_hash, email, full_name, role, is_active
                FROM users WHERE username = ? AND is_active = 1
            ''', (username,))
            
            user = self.db.cursor.fetchone()
            
            if not user:
                self.security.record_failed_attempt(username)
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", None
            
            user_dict = dict(user)
            
            if self.security.verify_password(password, user_dict['password_hash']):
                # تحديث آخر تسجيل دخول
                self.db.cursor.execute('''
                    UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
                ''', (user_dict['id'],))
                self.db.conn.commit()
                
                # إعادة تعيين المحاولات الفاشلة
                self.security.reset_failed_attempts(username)
                
                return True, "تم تسجيل الدخول بنجاح", user_dict
            else:
                self.security.record_failed_attempt(username)
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", None
                
        except Exception as e:
            print(f"Authentication error: {e}")
            return False, "خطأ في النظام", None
    
    def create_user(self, username: str, password: str, email: str = "", 
                   full_name: str = "", role: str = "user") -> tuple[bool, str]:
        """إنشاء مستخدم جديد"""
        # التحقق من قوة كلمة المرور
        is_strong, message = self.security.validate_password_strength(password)
        if not is_strong:
            return False, message
        
        try:
            password_hash = self.security.hash_password(password)
            
            self.db.cursor.execute('''
                INSERT INTO users (username, password_hash, email, full_name, role)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, password_hash, email, full_name, role))
            
            self.db.conn.commit()
            return True, "تم إنشاء المستخدم بنجاح"
            
        except Exception as e:
            if "UNIQUE constraint failed" in str(e):
                return False, "اسم المستخدم موجود بالفعل"
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"
