<StatisticsScreen>:
    MDBoxLayout:
        orientation: 'vertical'
        
        MDTopAppBar:
            title: "الإحصائيات والتقارير"
            elevation: 4
            pos_hint: {"top": 1}
            left_action_items: [['arrow-left', lambda x: app.root.ids.screen_manager.current = 'main']]
            right_action_items: [['refresh', lambda x: root.load_statistics()]]
        
        MDTabs:
            id: tabs
            on_tab_switch: root.on_tab_switch(*args)
            
            Tab:
                title: "الطلبات"
                
                MDBoxLayout:
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    
                    MDCard:
                        orientation: 'vertical'
                        size_hint: 1, 0.5
                        padding: dp(10)
                        elevation: 2
                        radius: [10, 10, 10, 10]
                        md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
                        
                        MDLabel:
                            text: "توزيع الطلبات حسب الفئة"
                            halign: "center"
                            size_hint_y: None
                            height: dp(30)
                            bold: True
                        
                        MDBoxLayout:
                            id: orders_chart_container
                            orientation: 'vertical'
                    
                    MDCard:
                        orientation: 'vertical'
                        size_hint: 1, 0.5
                        padding: dp(10)
                        elevation: 2
                        radius: [10, 10, 10, 10]
                        md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
                        
                        MDLabel:
                            text: "توزيع الطلبات حسب النوع"
                            halign: "center"
                            size_hint_y: None
                            height: dp(30)
                            bold: True
                        
                        MDBoxLayout:
                            id: orders_type_chart_container
                            orientation: 'vertical'
            
            Tab:
                title: "العملاء"
                
                MDBoxLayout:
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    
                    MDCard:
                        orientation: 'vertical'
                        size_hint: 1, 1
                        padding: dp(10)
                        elevation: 2
                        radius: [10, 10, 10, 10]
                        md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
                        
                        MDLabel:
                            text: "أفضل العملاء من حيث عدد الطلبات"
                            halign: "center"
                            size_hint_y: None
                            height: dp(30)
                            bold: True
                        
                        MDBoxLayout:
                            id: clients_chart_container
                            orientation: 'vertical'
            
            Tab:
                title: "الفئات"
                
                MDBoxLayout:
                    orientation: 'vertical'
                    padding: dp(10)
                    spacing: dp(10)
                    
                    MDCard:
                        orientation: 'vertical'
                        size_hint: 1, 1
                        padding: dp(10)
                        elevation: 2
                        radius: [10, 10, 10, 10]
                        md_bg_color: app.theme_cls.bg_darkest if app.theme_cls.theme_style == "Dark" else app.theme_cls.bg_light
                        
                        MDLabel:
                            text: "شعبية الفئات حسب عدد الطلبات"
                            halign: "center"
                            size_hint_y: None
                            height: dp(30)
                            bold: True
                        
                        MDBoxLayout:
                            id: categories_chart_container
                            orientation: 'vertical'