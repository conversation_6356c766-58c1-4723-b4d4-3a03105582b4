"""
مكونات واجهة المستخدم المحسنة
"""

from kivy.animation import Animation
from kivy.clock import Clock
from kivy.metrics import dp
from kivy.properties import StringProperty, NumericProperty, BooleanProperty
from kivy.uix.behaviors import ButtonBehavior
from kivymd.uix.card import MDCard
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from kivymd.uix.button import MDIconButton, MDRaisedButton
from kivymd.uix.progressbar import MDProgressBar
from kivymd.uix.spinner import MDSpinner
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar import Snackbar
from kivymd.uix.list import OneLineAvatarIconListItem, IconLeftWidget, IconRightWidget
from kivymd.uix.selectioncontrol import MDSwitch
from kivymd.color_definitions import colors
from kivymd.theming import ThemableBehavior


class AnimatedCard(MDCard, ButtonBehavior):
    """بطاقة متحركة مع تأثيرات بصرية"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 2
        self.radius = [10]
        self.ripple_behavior = True
        
    def on_press(self):
        """تأثير عند الضغط"""
        anim = Animation(elevation=8, duration=0.1)
        anim.start(self)
        
    def on_release(self):
        """تأثير عند الإفلات"""
        anim = Animation(elevation=2, duration=0.1)
        anim.start(self)


class LoadingCard(MDCard):
    """بطاقة تحميل مع مؤشر دوار"""
    
    text = StringProperty("جاري التحميل...")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = "vertical"
        self.size_hint = (None, None)
        self.size = (dp(200), dp(100))
        self.pos_hint = {"center_x": 0.5, "center_y": 0.5}
        self.elevation = 4
        self.radius = [10]
        self.padding = dp(20)
        self.spacing = dp(10)
        
        # إضافة مؤشر التحميل
        self.spinner = MDSpinner(
            size_hint=(None, None),
            size=(dp(30), dp(30)),
            pos_hint={"center_x": 0.5}
        )
        self.add_widget(self.spinner)
        
        # إضافة النص
        self.label = MDLabel(
            text=self.text,
            halign="center",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(30)
        )
        self.add_widget(self.label)
        
    def on_text(self, instance, value):
        """تحديث النص"""
        if hasattr(self, 'label'):
            self.label.text = value


class StatCard(AnimatedCard):
    """بطاقة إحصائية مع أيقونة وقيم"""
    
    title = StringProperty("")
    value = StringProperty("")
    icon = StringProperty("chart-line")
    color = StringProperty("primary")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = "horizontal"
        self.size_hint_y = None
        self.height = dp(80)
        self.padding = dp(16)
        self.spacing = dp(16)
        
        # إضافة الأيقونة
        self.icon_widget = MDIconButton(
            icon=self.icon,
            theme_icon_color="Custom",
            icon_color=self.theme_cls.primary_color,
            icon_size=dp(32)
        )
        self.add_widget(self.icon_widget)
        
        # إضافة النصوص
        text_layout = MDBoxLayout(
            orientation="vertical",
            spacing=dp(4)
        )
        
        self.title_label = MDLabel(
            text=self.title,
            font_style="Body1",
            theme_text_color="Primary",
            size_hint_y=None,
            height=dp(20)
        )
        text_layout.add_widget(self.title_label)
        
        self.value_label = MDLabel(
            text=self.value,
            font_style="H5",
            theme_text_color="Primary",
            bold=True,
            size_hint_y=None,
            height=dp(32)
        )
        text_layout.add_widget(self.value_label)
        
        self.add_widget(text_layout)
        
    def on_title(self, instance, value):
        """تحديث العنوان"""
        if hasattr(self, 'title_label'):
            self.title_label.text = value
            
    def on_value(self, instance, value):
        """تحديث القيمة"""
        if hasattr(self, 'value_label'):
            self.value_label.text = value
            
    def on_icon(self, instance, value):
        """تحديث الأيقونة"""
        if hasattr(self, 'icon_widget'):
            self.icon_widget.icon = value
            
    def animate_value_change(self):
        """تأثير متحرك عند تغيير القيمة"""
        if hasattr(self, 'value_label'):
            # تأثير نبضة
            anim = Animation(font_size=sp(24), duration=0.1) + Animation(font_size=sp(20), duration=0.1)
            anim.start(self.value_label)


class ActionButton(MDRaisedButton):
    """زر إجراء محسن مع تأثيرات"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.elevation = 3
        
    def on_press(self):
        """تأثير عند الضغط"""
        super().on_press()
        # تأثير اهتزاز خفيف
        anim = Animation(pos=(self.x + dp(2), self.y), duration=0.05) + \
               Animation(pos=(self.x - dp(2), self.y), duration=0.05) + \
               Animation(pos=(self.x, self.y), duration=0.05)
        anim.start(self)


class SwipeableListItem(OneLineAvatarIconListItem):
    """عنصر قائمة قابل للسحب مع إجراءات"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.swipe_distance = dp(80)
        self.is_swiped = False
        
        # إضافة أيقونة يسار
        self.left_icon = IconLeftWidget(icon="account")
        self.add_widget(self.left_icon)
        
        # إضافة أيقونة يمين (مخفية في البداية)
        self.right_icon = IconRightWidget(
            icon="delete",
            theme_icon_color="Custom",
            icon_color="red"
        )
        self.right_icon.opacity = 0
        self.add_widget(self.right_icon)
        
    def on_touch_move(self, touch):
        """معالجة حركة السحب"""
        if self.collide_point(*touch.pos):
            if touch.dx < -self.swipe_distance and not self.is_swiped:
                # سحب لليسار - إظهار خيار الحذف
                self.show_delete_option()
            elif touch.dx > self.swipe_distance and self.is_swiped:
                # سحب لليمين - إخفاء خيار الحذف
                self.hide_delete_option()
        return super().on_touch_move(touch)
        
    def show_delete_option(self):
        """إظهار خيار الحذف"""
        self.is_swiped = True
        anim = Animation(opacity=1, duration=0.2)
        anim.start(self.right_icon)
        
    def hide_delete_option(self):
        """إخفاء خيار الحذف"""
        self.is_swiped = False
        anim = Animation(opacity=0, duration=0.2)
        anim.start(self.right_icon)


class ProgressDialog(MDDialog):
    """حوار تقدم مع شريط تقدم"""
    
    progress_value = NumericProperty(0)
    
    def __init__(self, **kwargs):
        self.progress_bar = MDProgressBar(
            value=0,
            size_hint_y=None,
            height=dp(4)
        )
        
        content = MDBoxLayout(
            orientation="vertical",
            spacing=dp(16),
            size_hint_y=None,
            height=dp(60)
        )
        
        content.add_widget(MDLabel(
            text="جاري المعالجة...",
            halign="center",
            size_hint_y=None,
            height=dp(40)
        ))
        
        content.add_widget(self.progress_bar)
        
        super().__init__(
            type="custom",
            content_cls=content,
            auto_dismiss=False,
            **kwargs
        )
        
    def update_progress(self, value):
        """تحديث قيمة التقدم"""
        self.progress_value = value
        anim = Animation(value=value, duration=0.2)
        anim.start(self.progress_bar)
        
        if value >= 100:
            Clock.schedule_once(lambda dt: self.dismiss(), 0.5)


class NotificationSnackbar(Snackbar):
    """شريط إشعار محسن مع أيقونات"""
    
    notification_type = StringProperty("info")  # info, success, warning, error
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # تعيين الألوان حسب النوع
        colors_map = {
            "info": self.theme_cls.primary_color,
            "success": colors["Green"]["500"],
            "warning": colors["Orange"]["500"],
            "error": colors["Red"]["500"]
        }
        
        if self.notification_type in colors_map:
            self.bg_color = colors_map[self.notification_type]
            
        # إضافة أيقونة
        icons_map = {
            "info": "information",
            "success": "check-circle",
            "warning": "alert",
            "error": "alert-circle"
        }
        
        if self.notification_type in icons_map:
            # يمكن إضافة أيقونة هنا إذا كان مدعوماً في الإصدار المستخدم
            pass


class ThemeSelector(MDBoxLayout):
    """محدد السمة مع معاينة"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = "vertical"
        self.spacing = dp(16)
        self.padding = dp(16)
        
        # عنوان
        title = MDLabel(
            text="اختيار السمة",
            font_style="H6",
            size_hint_y=None,
            height=dp(40)
        )
        self.add_widget(title)
        
        # مفتاح السمة الداكنة/الفاتحة
        theme_switch_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(40),
            spacing=dp(16)
        )
        
        theme_switch_layout.add_widget(MDLabel(
            text="السمة الداكنة",
            size_hint_x=0.7
        ))
        
        self.theme_switch = MDSwitch()
        self.theme_switch.bind(active=self.on_theme_switch)
        theme_switch_layout.add_widget(self.theme_switch)
        
        self.add_widget(theme_switch_layout)
        
        # ألوان أساسية
        colors_label = MDLabel(
            text="الألوان الأساسية",
            font_style="Subtitle1",
            size_hint_y=None,
            height=dp(30)
        )
        self.add_widget(colors_label)
        
        # شبكة الألوان
        self.colors_layout = MDBoxLayout(
            orientation="horizontal",
            size_hint_y=None,
            height=dp(50),
            spacing=dp(8)
        )
        
        primary_colors = ["Blue", "Red", "Green", "Purple", "Orange"]
        for color in primary_colors:
            color_button = MDIconButton(
                icon="circle",
                theme_icon_color="Custom",
                icon_color=colors[color]["500"],
                on_release=lambda x, c=color: self.set_primary_color(c)
            )
            self.colors_layout.add_widget(color_button)
            
        self.add_widget(self.colors_layout)
        
    def on_theme_switch(self, instance, value):
        """تغيير السمة"""
        from kivymd.app import MDApp
        app = MDApp.get_running_app()
        app.theme_cls.theme_style = "Dark" if value else "Light"
        app.save_user_preferences()
        
    def set_primary_color(self, color):
        """تعيين اللون الأساسي"""
        from kivymd.app import MDApp
        app = MDApp.get_running_app()
        app.theme_cls.primary_palette = color
        app.save_user_preferences()


class SearchBar(MDBoxLayout):
    """شريط بحث محسن مع تأثيرات"""
    
    hint_text = StringProperty("البحث...")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.orientation = "horizontal"
        self.size_hint_y = None
        self.height = dp(56)
        self.spacing = dp(8)
        self.padding = [dp(16), dp(8)]
        
        # أيقونة البحث
        self.search_icon = MDIconButton(
            icon="magnify",
            size_hint_x=None,
            width=dp(40)
        )
        self.add_widget(self.search_icon)
        
        # حقل النص
        from kivymd.uix.textfield import MDTextField
        self.text_field = MDTextField(
            hint_text=self.hint_text,
            mode="fill",
            fill_color_normal=self.theme_cls.bg_light,
            fill_color_focus=self.theme_cls.bg_normal
        )
        self.add_widget(self.text_field)
        
        # أيقونة المسح
        self.clear_icon = MDIconButton(
            icon="close",
            size_hint_x=None,
            width=dp(40),
            opacity=0,
            on_release=self.clear_text
        )
        self.add_widget(self.clear_icon)
        
        # ربط الأحداث
        self.text_field.bind(text=self.on_text_change)
        
    def on_text_change(self, instance, value):
        """معالجة تغيير النص"""
        # إظهار/إخفاء أيقونة المسح
        if value:
            anim = Animation(opacity=1, duration=0.2)
        else:
            anim = Animation(opacity=0, duration=0.2)
        anim.start(self.clear_icon)
        
    def clear_text(self, *args):
        """مسح النص"""
        self.text_field.text = ""
        self.text_field.focus = True
        
    def on_hint_text(self, instance, value):
        """تحديث نص التلميح"""
        if hasattr(self, 'text_field'):
            self.text_field.hint_text = value
