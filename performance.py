"""
تحسينات الأداء وإدارة الذاكرة
"""

import gc
import psutil
import threading
import time
import weakref
from functools import wraps, lru_cache
from typing import Dict, List, Any, Callable, Optional
from kivy.clock import Clock
from kivy.logger import Logger
import traceback
from datetime import datetime, timedelta


class MemoryManager:
    """مدير الذاكرة"""
    
    def __init__(self):
        self.memory_threshold = 80  # نسبة مئوية
        self.cleanup_interval = 300  # 5 دقائق
        self.is_monitoring = False
        self.weak_references = weakref.WeakSet()
        
    def start_monitoring(self):
        """بدء مراقبة الذاكرة"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        
        def monitor_memory():
            while self.is_monitoring:
                try:
                    memory_percent = psutil.virtual_memory().percent
                    
                    if memory_percent > self.memory_threshold:
                        Logger.warning(f"Memory usage high: {memory_percent}%")
                        self.cleanup_memory()
                    
                    time.sleep(self.cleanup_interval)
                    
                except Exception as e:
                    Logger.error(f"Error in memory monitoring: {e}")
        
        thread = threading.Thread(target=monitor_memory, daemon=True)
        thread.start()
    
    def stop_monitoring(self):
        """إيقاف مراقبة الذاكرة"""
        self.is_monitoring = False
    
    def cleanup_memory(self):
        """تنظيف الذاكرة"""
        try:
            # تنظيف المراجع الضعيفة
            self.weak_references.clear()
            
            # تشغيل جامع القمامة
            collected = gc.collect()
            
            # تنظيف cache
            self.clear_all_caches()
            
            Logger.info(f"Memory cleanup completed. Collected {collected} objects.")
            
        except Exception as e:
            Logger.error(f"Error in memory cleanup: {e}")
    
    def clear_all_caches(self):
        """مسح جميع الـ caches"""
        try:
            # مسح LRU caches
            for obj in gc.get_objects():
                if hasattr(obj, 'cache_clear') and callable(obj.cache_clear):
                    try:
                        obj.cache_clear()
                    except:
                        pass
        except Exception as e:
            Logger.error(f"Error clearing caches: {e}")
    
    def get_memory_info(self) -> Dict:
        """الحصول على معلومات الذاكرة"""
        try:
            memory = psutil.virtual_memory()
            process = psutil.Process()
            
            return {
                'total_memory': memory.total,
                'available_memory': memory.available,
                'used_memory': memory.used,
                'memory_percent': memory.percent,
                'process_memory': process.memory_info().rss,
                'process_memory_percent': process.memory_percent()
            }
        except Exception as e:
            Logger.error(f"Error getting memory info: {e}")
            return {}
    
    def register_weak_reference(self, obj):
        """تسجيل مرجع ضعيف"""
        try:
            self.weak_references.add(obj)
        except:
            pass


class PerformanceMonitor:
    """مراقب الأداء"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
        
    def measure_time(self, func_name: str = None):
        """decorator لقياس وقت التنفيذ"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    name = func_name or f"{func.__module__}.{func.__name__}"
                    self.record_metric(name, execution_time)
                    
                    if execution_time > 1.0:  # تحذير للعمليات البطيئة
                        Logger.warning(f"Slow operation: {name} took {execution_time:.2f}s")
            
            return wrapper
        return decorator
    
    def record_metric(self, name: str, value: float):
        """تسجيل مقياس أداء"""
        if name not in self.metrics:
            self.metrics[name] = []
        
        self.metrics[name].append({
            'value': value,
            'timestamp': time.time()
        })
        
        # الاحتفاظ بآخر 100 قياس فقط
        if len(self.metrics[name]) > 100:
            self.metrics[name] = self.metrics[name][-100:]
    
    def get_metrics_summary(self) -> Dict:
        """الحصول على ملخص المقاييس"""
        summary = {}
        
        for name, values in self.metrics.items():
            if not values:
                continue
            
            times = [v['value'] for v in values]
            summary[name] = {
                'count': len(times),
                'avg': sum(times) / len(times),
                'min': min(times),
                'max': max(times),
                'total': sum(times)
            }
        
        return summary
    
    def get_system_metrics(self) -> Dict:
        """الحصول على مقاييس النظام"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'disk_percent': disk.percent,
                'uptime': time.time() - self.start_time
            }
        except Exception as e:
            Logger.error(f"Error getting system metrics: {e}")
            return {}


class ErrorHandler:
    """معالج الأخطاء"""
    
    def __init__(self, db_manager=None):
        self.db = db_manager
        self.error_count = 0
        self.last_errors = []
        self.max_stored_errors = 50
        
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """معالجة الاستثناءات"""
        try:
            error_info = {
                'type': exc_type.__name__,
                'message': str(exc_value),
                'traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)),
                'timestamp': datetime.now().isoformat()
            }
            
            self.error_count += 1
            self.last_errors.append(error_info)
            
            # الاحتفاظ بآخر الأخطاء فقط
            if len(self.last_errors) > self.max_stored_errors:
                self.last_errors = self.last_errors[-self.max_stored_errors:]
            
            # تسجيل الخطأ
            Logger.error(f"Exception: {error_info['type']}: {error_info['message']}")
            
            # حفظ في قاعدة البيانات إذا كانت متاحة
            if self.db:
                self._save_error_to_db(error_info)
            
        except Exception as e:
            Logger.error(f"Error in error handler: {e}")
    
    def _save_error_to_db(self, error_info: Dict):
        """حفظ الخطأ في قاعدة البيانات"""
        try:
            self.db.cursor.execute('''
                INSERT INTO error_log (error_type, message, traceback, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (
                error_info['type'],
                error_info['message'],
                error_info['traceback'],
                error_info['timestamp']
            ))
            self.db.conn.commit()
        except Exception as e:
            Logger.error(f"Error saving to database: {e}")
    
    def get_error_summary(self) -> Dict:
        """الحصول على ملخص الأخطاء"""
        return {
            'total_errors': self.error_count,
            'recent_errors': len(self.last_errors),
            'last_error': self.last_errors[-1] if self.last_errors else None
        }
    
    def clear_errors(self):
        """مسح سجل الأخطاء"""
        self.last_errors.clear()
        self.error_count = 0


class CacheManager:
    """مدير التخزين المؤقت"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.caches = {}
        
    def create_cache(self, name: str, max_size: int = None) -> Callable:
        """إنشاء cache جديد"""
        cache_size = max_size or self.max_size
        
        @lru_cache(maxsize=cache_size)
        def cached_function(func, *args, **kwargs):
            return func(*args, **kwargs)
        
        self.caches[name] = cached_function
        return cached_function
    
    def clear_cache(self, name: str):
        """مسح cache معين"""
        if name in self.caches:
            self.caches[name].cache_clear()
    
    def clear_all_caches(self):
        """مسح جميع الـ caches"""
        for cache in self.caches.values():
            cache.cache_clear()
    
    def get_cache_info(self, name: str) -> Dict:
        """الحصول على معلومات cache"""
        if name in self.caches:
            info = self.caches[name].cache_info()
            return {
                'hits': info.hits,
                'misses': info.misses,
                'maxsize': info.maxsize,
                'currsize': info.currsize,
                'hit_rate': info.hits / (info.hits + info.misses) if (info.hits + info.misses) > 0 else 0
            }
        return {}


class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    def __init__(self, db_manager):
        self.db = db_manager
        
    def analyze_database(self) -> Dict:
        """تحليل قاعدة البيانات"""
        try:
            analysis = {}
            
            # حجم قاعدة البيانات
            self.db.cursor.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
            db_size = self.db.cursor.fetchone()[0]
            analysis['database_size'] = db_size
            
            # إحصائيات الجداول
            self.db.cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in self.db.cursor.fetchall()]
            
            table_stats = {}
            for table in tables:
                self.db.cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = self.db.cursor.fetchone()[0]
                table_stats[table] = {'row_count': count}
            
            analysis['table_stats'] = table_stats
            
            # فحص الفهارس
            self.db.cursor.execute("SELECT name, tbl_name FROM sqlite_master WHERE type='index'")
            indexes = [{'name': row[0], 'table': row[1]} for row in self.db.cursor.fetchall()]
            analysis['indexes'] = indexes
            
            return analysis
            
        except Exception as e:
            Logger.error(f"Error analyzing database: {e}")
            return {}
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            # تحليل الجداول
            self.db.cursor.execute("ANALYZE")
            
            # ضغط قاعدة البيانات
            self.db.cursor.execute("VACUUM")
            
            # إعادة فهرسة
            self.db.cursor.execute("REINDEX")
            
            self.db.conn.commit()
            Logger.info("Database optimization completed")
            
        except Exception as e:
            Logger.error(f"Error optimizing database: {e}")
    
    def cleanup_old_data(self, days: int = 90):
        """تنظيف البيانات القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # حذف سجلات التدقيق القديمة
            self.db.cursor.execute(
                "DELETE FROM audit_log WHERE timestamp < ?",
                (cutoff_date.isoformat(),)
            )
            
            # حذف سجلات الأخطاء القديمة
            self.db.cursor.execute(
                "DELETE FROM error_log WHERE timestamp < ?",
                (cutoff_date.isoformat(),)
            )
            
            # حذف الإشعارات القديمة المقروءة
            self.db.cursor.execute(
                "DELETE FROM notifications WHERE is_read = 1 AND created_at < ?",
                (cutoff_date.isoformat(),)
            )
            
            self.db.conn.commit()
            Logger.info(f"Cleaned up data older than {days} days")
            
        except Exception as e:
            Logger.error(f"Error cleaning up old data: {e}")


class PerformanceProfiler:
    """مُحلل الأداء"""
    
    def __init__(self):
        self.profiles = {}
        
    def profile(self, name: str):
        """decorator للتحليل التفصيلي"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                import cProfile
                import pstats
                import io
                
                profiler = cProfile.Profile()
                profiler.enable()
                
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    profiler.disable()
                    
                    # تحليل النتائج
                    s = io.StringIO()
                    ps = pstats.Stats(profiler, stream=s)
                    ps.sort_stats('cumulative')
                    ps.print_stats(10)  # أفضل 10 دوال
                    
                    self.profiles[name] = s.getvalue()
            
            return wrapper
        return decorator
    
    def get_profile(self, name: str) -> str:
        """الحصول على تحليل الأداء"""
        return self.profiles.get(name, "No profile data available")
    
    def clear_profiles(self):
        """مسح تحاليل الأداء"""
        self.profiles.clear()


# إنشاء مثيلات عامة
memory_manager = MemoryManager()
performance_monitor = PerformanceMonitor()
error_handler = ErrorHandler()
cache_manager = CacheManager()


def setup_performance_monitoring(db_manager=None):
    """إعداد مراقبة الأداء"""
    try:
        # بدء مراقبة الذاكرة
        memory_manager.start_monitoring()
        
        # إعداد معالج الأخطاء
        if db_manager:
            error_handler.db = db_manager
            
            # إنشاء جدول سجل الأخطاء
            db_manager.cursor.execute('''
                CREATE TABLE IF NOT EXISTS error_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    error_type TEXT NOT NULL,
                    message TEXT NOT NULL,
                    traceback TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            db_manager.conn.commit()
        
        # تعيين معالج الاستثناءات العام
        import sys
        sys.excepthook = error_handler.handle_exception
        
        Logger.info("Performance monitoring setup completed")
        
    except Exception as e:
        Logger.error(f"Error setting up performance monitoring: {e}")


def cleanup_performance_monitoring():
    """تنظيف مراقبة الأداء"""
    try:
        memory_manager.stop_monitoring()
        cache_manager.clear_all_caches()
        Logger.info("Performance monitoring cleanup completed")
    except Exception as e:
        Logger.error(f"Error cleaning up performance monitoring: {e}")
